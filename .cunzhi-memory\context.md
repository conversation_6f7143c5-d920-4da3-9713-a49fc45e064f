# 项目上下文信息

## 项目架构和技术栈
- 项目当前状态：C++重构完成，功能稳定运行，保存图像功能已优化（异步保存、最高画质、字体大小基于图像分辨率）
- 项目架构：分层渲染架构，VideoDisplayWidget显示视频，PaintingOverlay处理绘图交互
- 技术栈：Qt6 + OpenCV 4.10.0 + 海康SDK，CMake构建，QtConcurrent异步处理
## 核心功能模块
- 相机控制模块：海康威视SDK封装，多线程图像采集，相机管理器统一接口
- 绘制功能模块：直线、圆形、平行线、两点线等绘制工具，与Python版本样式一致
- 性能优化：DrawingContext缓存系统，局部刷新机制，60fps更新频率限制
## 当前功能状态
- 保存图像功能：已完全优化（异步保存、100%画质、字体1.5%图像高度、padding fontSize*0.5）
- 绘制功能：直线、圆形、平行线、两点线、线段等，样式与Python版本一致
- 测量功能：复合测量（点线距离、点圆距离、线圆关系、线段夹角）
- 性能优化：DrawingContext缓存、局部刷新、60fps限制、Tab可见性控制

# MutiCamApp 多摄像头应用软件架构说明书

## 1. 项目概述

MutiCamApp是一个基于PyQt5开发的多摄像头工业视觉测量应用程序，主要用于精密测量和图像分析。该软件支持多个摄像头同时工作，提供实时图像显示、精确测量、图形绘制和数据管理等功能。

## 2. 技术栈

### 2.1 核心技术
- **GUI框架**: PyQt5 - 提供现代化的用户界面
- **图像处理**: OpenCV (cv2) - 图像处理和计算机视觉算法
- **数值计算**: NumPy - 高性能数值计算
- **相机控制**: 海康威视MVS SDK - 工业相机控制
- **数据处理**: Python标准库 (json, datetime等)

### 2.2 开发语言
- **主要语言**: Python 3.x
- **UI设计**: Qt Designer (.ui文件)
- **配置文件**: JSON格式

## 3. 软件架构设计

### 3.1 整体架构模式

软件采用**分层架构**和**模块化设计**，主要分为以下几层：

```
┌─────────────────────────────────────┐
│           用户界面层 (UI Layer)        │
├─────────────────────────────────────┤
│         业务逻辑层 (Business Layer)    │
├─────────────────────────────────────┤
│         数据处理层 (Data Layer)        │
├─────────────────────────────────────┤
│         硬件抽象层 (Hardware Layer)    │
└─────────────────────────────────────┘
```

### 3.2 核心模块架构

#### 3.2.1 主应用模块 (MainApp)
- **文件**: `main.py`
- **职责**: 应用程序入口，主窗口管理，模块协调
- **关键类**: `MainApp(QMainWindow, Ui_MainWindow)`

#### 3.2.2 用户界面模块
- **文件**: `mainwindow.py`, `mainwindow.ui`
- **职责**: UI布局定义，界面元素管理
- **特点**: 由Qt Designer生成，支持多选项卡界面

#### 3.2.3 相机控制模块
- **文件**: `Tools/camera_controller.py`, `Tools/camera_thread.py`
- **职责**: 相机硬件控制，图像采集，多线程处理
- **关键类**: 
  - `HikiCamera`: 相机硬件抽象
  - `CameraThread`: 相机采集线程

#### 3.2.4 测量与绘图模块
- **文件**: `Tools/measurement_manager.py`, `Tools/drawing_manager.py`
- **职责**: 图形绘制，精密测量，几何计算
- **关键类**:
  - `MeasurementManager`: 测量逻辑管理
  - `DrawingManager`: 绘图操作管理
  - `LayerManager`: 图层管理

#### 3.2.5 配置管理模块
- **文件**: `Tools/settings_manager.py`
- **职责**: 参数配置，设置持久化
- **关键类**: `SettingsManager`

#### 3.2.6 日志管理模块
- **文件**: `Tools/log_manager.py`
- **职责**: 操作日志，错误追踪，调试信息
- **关键类**: `LogManager`

#### 3.2.7 界面增强模块
- **文件**: `Tools/grid_container.py`
- **职责**: 网格显示，视图缩放，界面交互增强
- **关键类**: `GridContainer`, `GridOverlay`

## 4. 详细架构分析

### 4.1 数据流架构

```
相机硬件 → CameraThread → MainApp → UI显示
    ↓
图像处理 → MeasurementManager → 测量结果
    ↓
DrawingManager → 图形渲染 → 界面更新
```

### 4.2 线程架构

- **主线程**: UI界面，用户交互
- **相机线程**: 图像采集 (CameraThread)
- **处理线程**: 图像处理，测量计算

### 4.3 事件驱动架构

软件采用事件驱动模式，主要事件类型：
- **鼠标事件**: 绘图操作，测量点选择
- **相机事件**: 图像帧就绪信号
- **UI事件**: 按钮点击，参数修改
- **系统事件**: 窗口调整，焦点变化

### 4.4 设计模式应用

#### 4.4.1 单例模式
- `LogManager`: 全局日志管理
- `SettingsManager`: 配置管理

#### 4.4.2 观察者模式
- 相机线程通过信号槽机制通知UI更新
- 测量结果变化通知界面刷新

#### 4.4.3 策略模式
- 不同测量类型 (DrawingType) 对应不同处理策略
- 多种图像处理算法可切换

#### 4.4.4 工厂模式
- 相机实例创建
- 测量对象创建

## 5. 核心功能模块

### 5.1 多摄像头管理
- 支持垂直、左侧、前方三个视角
- 独立的相机线程管理
- 实时图像显示和同步

### 5.2 精密测量功能
- **点测量**: 坐标定位
- **线段测量**: 长度、角度计算
- **圆形测量**: 直径、半径、圆心定位
- **复合测量**: 点到线、线到圆等距离测量

### 5.3 图像处理功能
- **边缘检测**: Canny算法
- **直线检测**: HoughLinesP算法
- **圆形检测**: HoughCircles算法
- **图像增强**: 对比度、亮度调整

### 5.4 用户交互功能
- **多视图显示**: 主界面和选项卡双重显示
- **网格辅助**: 可调节网格密度
- **缩放功能**: 鼠标滚轮缩放
- **绘图工具**: 多种绘图模式

## 6. 数据管理架构

### 6.1 配置数据
- **存储位置**: `Settings/settings.json`
- **内容**: 相机参数、检测参数、UI设置
- **管理方式**: JSON格式，实时保存

### 6.2 日志数据
- **存储位置**: `Logs/` 目录
- **格式**: 按日期分文件存储
- **内容**: 操作记录、错误信息、性能数据

### 6.3 图像数据
- **实时处理**: 内存中处理，不持久化
- **保存功能**: 支持手动保存当前视图
- **格式支持**: 常见图像格式

## 7. 性能优化设计

### 7.1 内存管理
- 图像帧缓冲机制
- 对象池复用
- 及时释放不用资源

### 7.2 渲染优化
- 帧率控制 (20fps)
- 缓存机制减少重复渲染
- 跳帧处理降低CPU负担

### 7.3 线程优化
- 相机采集与UI分离
- 异步处理避免界面卡顿
- 线程池管理

## 8. 扩展性设计

### 8.1 模块化架构
- 各功能模块独立，便于单独升级
- 标准接口设计，支持插件扩展

### 8.2 配置驱动
- 参数外部化，无需修改代码
- 支持运行时配置更新

### 8.3 算法可替换
- 图像处理算法模块化
- 支持集成第三方算法库 (Halcon, VisionMaster)

## 9. 安全性考虑

### 9.1 错误处理
- 完善的异常捕获机制
- 错误日志记录
- 优雅降级处理

### 9.2 资源保护
- 相机资源独占访问
- 内存泄漏防护
- 线程安全保证

## 10. UI布局架构

### 10.1 整体布局结构

#### 主窗口架构
- **基础容器**：`QMainWindow`作为主窗口容器
- **核心组件**：`QTabWidget`实现多标签页切换
- **布局管理**：采用`QVBoxLayout`和`QHBoxLayout`嵌套布局

#### 标签页组织
```
QTabWidget
├── 主界面 (Main Interface)
├── 垂直视图 (Vertical View)
├── 左侧视图 (Left View)
├── 对向视图 (Opposite View)
└── 参数设置 (Parameter Settings)
```

### 10.2 主界面布局

#### 视图区域布局
```
QHBoxLayout (主水平布局)
├── 垂直视图组 (QGroupBox)
│   └── 图像显示区域 (QLabel)
├── 左侧视图组 (QGroupBox)
│   └── 图像显示区域 (QLabel)
├── 对向视图组 (QGroupBox)
│   └── 图像显示区域 (QLabel)
└── 测量功能组 (QGroupBox)
    ├── 绘画功能区
    ├── 网格功能区
    ├── 自动测量区
    └── 运行控制区
```

#### 功能区域组织
- **绘画功能区**：包含点、直线、圆形、平行线等绘制工具
- **网格功能区**：网格密度设置和显示控制
- **自动测量区**：直线/圆查找和结果管理
- **运行控制区**：开始/停止测量和图像保存

### 10.3 独立视图标签页

#### 统一布局模式
每个视图标签页采用相同的布局结构：
```
QVBoxLayout (主垂直布局)
├── 图像显示区域 (QLabel)
├── 保存图像按钮
└── QHBoxLayout (功能区水平布局)
    ├── 网格功能组 (QGroupBox)
    ├── 自动测量组 (QGroupBox)
    └── 绘画功能组 (QGroupBox)
```

#### 功能组详细结构
- **网格功能组**：
  - 网格密度输入框
  - 取消网格按钮
- **自动测量组**：
  - 直线/圆查找按钮
  - 撤销上步结果按钮
- **绘画功能组**：
  - 绘制工具按钮组（点、直线、圆形等）
  - 操作按钮组（撤销、清空、标定）

### 10.4 参数设置界面

#### 设置分组结构
```
QVBoxLayout (主垂直布局)
├── 显示设置组 (QGroupBox)
│   ├── 界面总宽设置
│   └── 界面总高设置
├── 相机采集参数组 (QGroupBox)
│   ├── 垂直相机SN码
│   ├── 左侧相机SN码
│   └── 对向相机SN码
└── 自动测量参数组 (QGroupBox)
    ├── 直线查找参数子组
    │   ├── 边缘低阈值
    │   ├── 边缘高阈值
    │   ├── 最小线长
    │   └── 最大间隙
    └── 圆查找参数子组
        ├── 边缘低阈值
        ├── 边缘高阈值
        └── 圆查找阈值
```

### 10.5 UI设计特点

#### 响应式布局
- **弹性布局**：使用`stretch`参数实现比例分配
- **间距控制**：通过`QSpacerItem`控制组件间距
- **自适应调整**：支持窗口大小变化时的布局调整

#### 用户体验设计
- **功能分组**：相关功能集中在同一组框内
- **视觉层次**：通过字体大小和粗细区分重要性
- **操作便捷**：常用功能按钮布局在易访问位置

#### 一致性设计
- **统一字体**：所有控件使用11号粗体字体
- **标准间距**：采用统一的布局间距标准
- **对齐方式**：标签文本统一左对齐垂直居中

### 10.6 布局扩展性

#### 模块化设计
- **独立功能组**：每个功能组可独立添加或移除
- **标签页扩展**：可轻松添加新的视图标签页
- **参数组扩展**：设置界面支持新增参数分组

#### 国际化支持
- **文本分离**：UI文本通过属性设置，便于国际化
- **布局适应**：布局设计考虑不同语言文本长度差异

## 11. 部署架构

### 11.1 开发环境
- Python 3.x
- PyQt5
- OpenCV
- 海康威视MVS SDK
- NumPy等科学计算库

### 11.2 运行环境
- Windows/Linux操作系统
- 海康威视相机驱动
- 足够的内存和CPU资源

### 11.3 部署方式
- 源码部署：直接运行Python脚本
- 打包部署：使用PyInstaller等工具打包成可执行文件

### 11.4 依赖管理
- Python环境要求
- 第三方库依赖
- 硬件驱动要求

### 11.5 文件结构
```
MutiCamApp/
├── main.py              # 主程序入口
├── mainwindow.py        # UI定义文件
├── mainwindow.ui        # Qt Designer文件
├── Development/         # 相机SDK封装
├── Tools/              # 核心功能模块
├── Settings/           # 配置文件
└── Logs/              # 日志文件
```

## 12. 未来改进方向

### 12.1 性能提升
- C++核心算法重构
- GPU加速图像处理
- 多核并行计算

### 12.2 功能增强
- 更多测量算法
- 3D测量支持
- 自动化测量流程

### 12.3 用户体验
- 更直观的操作界面
- 快捷键支持
- 操作向导功能

---

**文档版本**: 1.0  
**创建日期**: 2024年  
**适用版本**: MutiCamApp 当前版本  
**维护者**: 开发团队
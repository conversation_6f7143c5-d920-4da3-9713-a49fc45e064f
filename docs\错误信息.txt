[main] 正在生成文件夹: d:/AppData/Documents/GitHub/MutiCamApp_Cpp/build all
[build] 正在启动生成
[proc] 正在执行命令: C:\Qt\Tools\CMake_64\bin\cmake.EXE --build d:/AppData/Documents/GitHub/MutiCamApp_Cpp/build --config Debug --target all --
[build] [1/11   9% :: 0.808] Automatic MOC and UIC for target MutiCamApp
[build] [10/11  18% :: 2.895] Building CXX object CMakeFiles\MutiCamApp.dir\src\core\window_manager\window_interfaces.cpp.obj
[build] [10/11  27% :: 4.112] Building CXX object CMakeFiles\MutiCamApp.dir\src\core\camera\hikvision_camera.cpp.obj
[build] [10/11  36% :: 4.119] Building CXX object CMakeFiles\MutiCamApp.dir\src\MutiCamApp.cpp.obj
[build] FAILED: CMakeFiles/MutiCamApp.dir/src/MutiCamApp.cpp.obj 
[build] C:\PROGRA~1\MIB055~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build\MutiCamApp_autogen\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\opencv\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\hikvision\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\core -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 /wd4828 -Zc:__cplusplus -permissive- -utf-8 /showIncludes /FoCMakeFiles\MutiCamApp.dir\src\MutiCamApp.cpp.obj /FdCMakeFiles\MutiCamApp.dir\ /FS -c D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): note: 参见“TwoLinesObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): note: 参见“TwoLinesObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2710): error C2664: “void VideoDisplayWidget::setLinesData(const int)”: 无法将参数 1 从“T”转换为“const int”
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::LineObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2710): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): note: 参见“VideoDisplayWidget::setLinesData”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2710): note: 尝试匹配参数列表“(T)”时
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::LineObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2714): error C2664: “void VideoDisplayWidget::setCirclesData(const int)”: 无法将参数 1 从“T”转换为“const int”
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::CircleObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2714): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): note: 参见“VideoDisplayWidget::setCirclesData”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2714): note: 尝试匹配参数列表“(T)”时
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::CircleObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2718): error C2664: “void VideoDisplayWidget::setFineCirclesData(const int)”: 无法将参数 1 从“T”转换为“const int”
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::FineCircleObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2718): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): note: 参见“VideoDisplayWidget::setFineCirclesData”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2718): note: 尝试匹配参数列表“(T)”时
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::FineCircleObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2722): error C2664: “void VideoDisplayWidget::setParallelLinesData(const int)”: 无法将参数 1 从“T”转换为“const int”
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::ParallelObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2722): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): note: 参见“VideoDisplayWidget::setParallelLinesData”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2722): note: 尝试匹配参数列表“(T)”时
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::ParallelObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2726): error C2664: “void VideoDisplayWidget::setTwoLinesData(const int)”: 无法将参数 1 从“T”转换为“const int”
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::TwoLinesObject>
[build]         ]
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2726): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): note: 参见“VideoDisplayWidget::setTwoLinesData”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\MutiCamApp.cpp(2726): note: 尝试�ヅ洳问�列表“(T)”时
[build]         with
[build]         [
[build]             T=QList<MutiCamApp::TwoLinesObject>
[build]         ]
[build] [10/11  45% :: 4.165] Building CXX object CMakeFiles\MutiCamApp.dir\src\core\camera\camera_thread.cpp.obj
[build] [10/11  54% :: 4.224] Building CXX object CMakeFiles\MutiCamApp.dir\src\dependencies_test.cpp.obj
[build] [10/11  63% :: 4.234] Building CXX object CMakeFiles\MutiCamApp.dir\src\core\drawing\drawing_manager.cpp.obj
[build] [10/11  72% :: 4.316] Building CXX object CMakeFiles\MutiCamApp.dir\src\core\camera\camera_manager.cpp.obj
[build] [10/11  81% :: 4.325] Building CXX object CMakeFiles\MutiCamApp.dir\src\main.cpp.obj
[build] FAILED: CMakeFiles/MutiCamApp.dir/src/main.cpp.obj 
[build] C:\PROGRA~1\MIB055~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build\MutiCamApp_autogen\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\opencv\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\hikvision\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\core -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 /wd4828 -Zc:__cplusplus -permissive- -utf-8 /showIncludes /FoCMakeFiles\MutiCamApp.dir\src\main.cpp.obj /FdCMakeFiles\MutiCamApp.dir\ /FS -c D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\main.cpp
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): note: 参见“TwoLinesObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): note: 参见“TwoLinesObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\main.cpp(23): warning C4996: 'Qt::AA_EnableHighDpiScaling': High-DPI scaling is always enabled. This attribute no longer has any effect.
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\main.cpp(24): warning C4996: 'Qt::AA_UseHighDpiPixmaps': High-DPI pixmaps are always enabled. This attribute no longer has any effect.
[build] [10/11  90% :: 4.730] Building CXX object CMakeFiles\MutiCamApp.dir\MutiCamApp_autogen\mocs_compilation.cpp.obj
[build] FAILED: CMakeFiles/MutiCamApp.dir/MutiCamApp_autogen/mocs_compilation.cpp.obj 
[build] C:\PROGRA~1\MIB055~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\build\MutiCamApp_autogen\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\opencv\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\third_party\hikvision\include -ID:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\core -external:IC:\Qt\6.8.0\msvc2022_64\include\QtWidgets -external:IC:\Qt\6.8.0\msvc2022_64\include -external:IC:\Qt\6.8.0\msvc2022_64\include\QtCore -external:IC:\Qt\6.8.0\msvc2022_64\mkspecs\win32-msvc -external:IC:\Qt\6.8.0\msvc2022_64\include\QtGui -external:W0 /DWIN32 /D_WINDOWS /W3 /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /MDd /Zi /Ob0 /Od /RTC1 -std:c++17 /utf-8 /wd4828 -Zc:__cplusplus -permissive- -utf-8 /showIncludes /FoCMakeFiles\MutiCamApp.dir\MutiCamApp_autogen\mocs_compilation.cpp.obj /FdCMakeFiles\MutiCamApp.dir\ /FS -c D:\AppData\Documents\GitHub\MutiCamApp_Cpp\build\MutiCamApp_autogen\mocs_compilation.cpp
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(44): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(45): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(46): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(47): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): note: 参见“TwoLinesObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2144: 语法错误:“<error type>”的前面应有“;”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(48): error C2059: 语法错误:“)”
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2065: “LineObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): error C2923: "QVector": "LineObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(89): note: 参见“LineObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2065: “CircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): error C2923: "QVector": "CircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(90): note: 参见“CircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2065: “FineCircleObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): error C2923: "QVector": "FineCircleObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(91): note: 参见“FineCircleObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2065: “ParallelObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): error C2923: "QVector": "ParallelObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(92): note: 参见“ParallelObject”的声明
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “MutiCamApp”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2653: “MutiCamApp”: 不是类或命名空间名称
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2065: “TwoLinesObject”: 未声明的标识符
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): error C2923: "QVector": "TwoLinesObject" 不是参数 "T" 的有效 模板 类型参数
[build] D:\AppData\Documents\GitHub\MutiCamApp_Cpp\src\VideoDisplayWidget.h(93): note: 参见“TwoLinesObject”的声明
[build] ninja: build stopped: subcommand failed.
[proc] 命令“C:\Qt\Tools\CMake_64\bin\cmake.EXE --build d:/AppData/Documents/GitHub/MutiCamApp_Cpp/build --config Debug --target all --”已退出，代码为 1
[driver] 生成完毕: 00:00:04.798
[build] 生成已完成，退出代码为 1

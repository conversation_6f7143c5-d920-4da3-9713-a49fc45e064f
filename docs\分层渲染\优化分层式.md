
### **最终方案：引入独立的透明“绘画层” (Painting Overlay)**

**核心思想：**

我们将把“显示视频”和“交互式绘图”这两个职责彻底分离开来。

1.  **`VideoDisplayWidget` (底层)**: 它的职责被大大简化，只负责显示一个 `QPixmap`。这个 `QPixmap` 将是“烘焙”好的静态背景，包含了视频帧和所有**已完成**的图形。它不再处理任何鼠标事件或实时绘图。

2.  **`PaintingOverlay` (顶层，新类)**: 这是一个全新的、**完全透明**的 `QWidget`。
    *   它将被放置在 `VideoDisplayWidget` 的**正上方**，大小完全一致。
    *   它将接管**所有**的鼠标事件 (`mousePress`, `mouseMove`)。
    *   它的 `paintEvent` **只绘制**动态内容：即**当前正在预览的图形**和**选择高亮框**。
    *   因为这个覆盖层是透明的，并且只绘制极少量的内容（比如一条线），所以它的重绘成本极低，速度飞快。

**工作流程的改变：**

*   **视频帧更新时**: `MutiCamApp` 将新的视频帧传递给 `VideoDisplayWidget`，并触发一次背景的“烘焙”。
*   **鼠标移动进行预览时**: `MutiCamApp` 只需通知 `PaintingOverlay` 更新。`PaintingOverlay` 的 `paintEvent` 被调用，它只在自己的透明背景上画一条线，然后Qt将这个透明层叠加在未发生变化的 `VideoDisplayWidget` 之上。**昂贵的背景重绘完全没有发生**。
*   **一次绘制完成时**: `PaintingOverlay` 发出信号，通知 `MutiCamApp`。`MutiCamApp` 随即命令 `VideoDisplayWidget` 将这个新完成的图形“烘焙”到它的背景缓存中，并命令 `PaintingOverlay` 清除这个刚刚完成的图形（因为它现在已经成为背景的一部分了）。

这个架构保证了高频的交互操作只涉及低成本的重绘，从而彻底解决性能瓶颈。

---

### **代码重构实施方案**

这是一个比较大的重构，但逻辑清晰。我会提供关键代码的实现，您可以将其整合到您的项目中。

#### **第1步：创建一个新的 `PaintingOverlay` 类**

这个类将包含之前 `VideoDisplayWidget` 的大部分绘图逻辑。

**`PaintingOverlay.h`**
```cpp
#ifndef PAINTINGOVERLAY_H
#define PAINTINGOVERLAY_H

#include <QWidget>
#include "VideoDisplayWidget.h" // 包含定义以使用其中的结构体和枚举

class PaintingOverlay : public QWidget
{
    Q_OBJECT

public:
    explicit PaintingOverlay(QWidget *parent = nullptr);

    // 公共接口，由 MutiCamApp 调用
    void startDrawing(VideoDisplayWidget::DrawingTool tool);
    void stopDrawing();
    void clearAllDrawings();
    void undoLastDrawing();
    void deleteSelectedObjects();
    void createLineFromSelectedPoints();
    void setTransforms(const QPointF& offset, double scale); // 用于同步坐标系

    VideoDisplayWidget::DrawingState getDrawingState() const;
    void setDrawingState(const VideoDisplayWidget::DrawingState& state);
    
    // 提供一个方法给VideoDisplayWidget来绘制静态内容
    void paintCompletedItems(QPainter* painter) const;

signals:
    void drawingCompleted(const QString& result); // 绘图完成信号
    void selectionChanged(const QString& info);   // 选择变化信号

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;

private:
    // --- 将 VideoDisplayWidget 中的所有绘图相关成员变量和方法都移动到这里 ---
    bool m_isDrawingMode;
    VideoDisplayWidget::DrawingTool m_currentDrawingTool;
    
    // 绘图数据
    QVector<QPointF> m_points;
    QVector<VideoDisplayWidget::LineObject> m_lines;
    // ... 其他所有绘图数据 QVector ...
    
    // 当前正在绘制的数据
    VideoDisplayWidget::LineObject m_currentLine;
    bool m_hasCurrentLine;
    // ... 其他所有 m_current... 和 m_hasCurrent...

    // 鼠标预览位置
    QPointF m_currentMousePos;
    bool m_hasValidMousePos;

    // 历史记录
    QStack<VideoDisplayWidget::DrawingAction> m_drawingHistory;

    // 选择状态
    bool m_selectionEnabled;
    QSet<int> m_selectedPoints;
    // ... 其他所有 m_selected...
    
    // 坐标变换参数
    QPointF m_imageOffset;
    double m_scaleFactor;

    // 私有方法
    // 将 VideoDisplayWidget 的所有 draw..., handle...Click, hitTest... 等方法都移到这里
    // ...
};

#endif // PAINTINGOVERLAY_H
```

**`PaintingOverlay.cpp`**
1.  **构造函数**:
    ```cpp
    PaintingOverlay::PaintingOverlay(QWidget *parent) : QWidget(parent)
    {
        // 关键：设置透明背景，并让鼠标事件穿透到下层（如果需要）
        setAttribute(Qt::WA_TranslucentBackground);
        setAttribute(Qt::WA_NoSystemBackground);
        setMouseTracking(true);
    }
    ```
2.  **方法迁移**: 将 `VideoDisplayWidget.cpp` 中所有与绘图逻辑、鼠标事件处理、命中测试、几何计算相关的函数 **全部剪切并粘贴** 到 `PaintingOverlay.cpp` 中。
3.  **修改 `paintEvent`**:
    ```cpp
    void PaintingOverlay::paintEvent(QPaintEvent *event)
    {
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing, true);

        // 设置从 MutiCamApp 传递过来的坐标变换
        painter.translate(m_imageOffset);
        painter.scale(m_scaleFactor, m_scaleFactor);

        // 只绘制动态内容：预览线和选择高亮
        if (m_isDrawingMode) {
            // ... 绘制 m_current... 预览图形的逻辑 ...
        }
        if (m_selectionEnabled) {
            // ... 绘制 m_selected... 高亮框的逻辑 ...
        }
    }
    ```
4.  **新增 `paintCompletedItems` 方法**:
    ```cpp
    void PaintingOverlay::paintCompletedItems(QPainter* painter) const
    {
        // 这个方法用于将已完成的图形绘制到 VideoDisplayWidget 的背景缓存上
        // 逻辑就是从原来的 paintEvent 中提取出来的静态图形绘制部分
        drawPoints(*painter, ...);
        drawLines(*painter, ...);
        // ...
    }
    ```
5.  **修改 `commitDrawingAction`**:
    ```cpp
    void PaintingOverlay::commitDrawingAction(...)
    {
        // ... (将图形添加到 m_lines, m_circles 等列表中) ...
        m_drawingHistory.push(...);

        // 不再调用 update()，而是发射信号通知外部
        emit drawingCompleted(result);
    }
    ```

#### **第2步：极大简化 `VideoDisplayWidget`**

它现在只做一件事：显示一个 `QPixmap`。

**`VideoDisplayWidget.h`**
```cpp
#ifndef VIDEODISPLAYWIDGET_H
#define VIDEODISPLAYWIDGET_H

#include <QLabel>
#include <QPixmap>
// ... 其他必要的包含

class PaintingOverlay; // 前向声明

class VideoDisplayWidget : public QLabel
{
    Q_OBJECT

public:
    explicit VideoDisplayWidget(QWidget *parent = nullptr);
    void setVideoFrame(const QPixmap& pixmap);
    void updateStaticDrawings(const PaintingOverlay* overlay); // 新的公共槽函数

    // 仍然需要提供坐标转换的辅助函数
    QPointF getImageOffset() const;
    double getScaleFactor() const;

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    QPixmap m_videoFrame;
    QPixmap m_compositionBuffer; // 静态背景缓存
};

#endif // VIDEODISPLAYWIDGET_H
```

**`VideoDisplayWidget.cpp`**
```cpp
#include "VideoDisplayWidget.h"
#include "PaintingOverlay.h"
#include <QPainter>

VideoDisplayWidget::VideoDisplayWidget(QWidget *parent) : QLabel(parent) {
    // 构造函数可以很简单
}

void VideoDisplayWidget::setVideoFrame(const QPixmap& pixmap)
{
    m_videoFrame = pixmap;
    // 注意：这里不再调用 update()，而是等待 MutiCamApp 来决定何时更新背景
}

void VideoDisplayWidget::updateStaticDrawings(const PaintingOverlay* overlay)
{
    if (m_videoFrame.isNull()) return;

    // 创建高DPI感知的缓存
    qreal dpr = devicePixelRatioF();
    m_compositionBuffer = QPixmap(size() * dpr);
    m_compositionBuffer.setDevicePixelRatio(dpr);
    m_compositionBuffer.fill(Qt::transparent);

    QPainter cacheP(&m_compositionBuffer);
    cacheP.scale(dpr, dpr); // 缩放到物理像素

    // 1. 绘制视频帧
    QRectF targetRect(QPointF(0,0), size()); // 绘制到整个控件区域
    cacheP.drawPixmap(targetRect, m_videoFrame, m_videoFrame.rect());

    // 2. 让 Overlay 把已完成的图形画到我们的缓存上
    overlay->paintCompletedItems(&cacheP);

    // 触发一次重绘，将新烘焙的背景显示出来
    update();
}

void VideoDisplayWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    // 任务极其简单：将缓存画上去
    if (!m_compositionBuffer.isNull()) {
        painter.drawPixmap(0, 0, m_compositionBuffer);
    } else if (!m_videoFrame.isNull()) {
        // 在第一次缓存生成前，先显示原始视频帧
        painter.drawPixmap(rect(), m_videoFrame, m_videoFrame.rect());
    }
}

// getImageOffset() 和 getScaleFactor() 的实现保持不变
```

#### **第3步：在 `MutiCamApp` 中重新组织和连接**

这是将新架构组合在一起的地方。

**`MutiCamApp.h`**:
*   为每个视图添加 `PaintingOverlay*` 成员变量，例如 `PaintingOverlay* m_verticalOverlay;`。

**`MutiCamApp.cpp`**:

1.  **`initializeVideoDisplayWidgets()`**:
    ```cpp
    // 对每个视图，例如垂直视图
    auto container = new QWidget();
    auto layout = new QStackedLayout(container);
    layout->setStackingMode(QStackedLayout::StackAll); // 关键：让控件叠加

    m_verticalDisplayWidget = new VideoDisplayWidget(container);
    m_verticalOverlay = new PaintingOverlay(container); // 创建Overlay

    layout->addWidget(m_verticalDisplayWidget); // 底层
    layout->addWidget(m_verticalOverlay);     // 顶层

    // 将 container 放入主布局中
    ui->gridLayout_vertical->addWidget(container, 0, 0);
    ```

2.  **`connectSignalsAndSlots()`**:
    ```cpp
    // 连接Overlay的信号
    connect(m_verticalOverlay, &PaintingOverlay::drawingCompleted, this, [=](const QString& result){
        // 绘图完成，通知底层Widget更新它的静态背景
        m_verticalDisplayWidget->updateStaticDrawings(m_verticalOverlay);
        // 同时也要更新配对视图
        m_verticalDisplayWidget2->updateStaticDrawings(m_verticalOverlay);
    });

    // 将UI按钮的点击信号连接到Overlay
    connect(ui->btnDrawPoint, &QPushButton::clicked, m_verticalOverlay, &PaintingOverlay::startDrawing);
    ```

3.  **`onCameraFrameReady()`**:
    ```cpp
    void MutiCamApp::onCameraFrameReady(const QString& cameraId, const cv::Mat& frame)
    {
        // ...
        VideoDisplayWidget* mainWidget = getVideoDisplayWidget(cameraId);
        PaintingOverlay* mainOverlay = getPaintingOverlay(cameraId); // 需要一个类似的辅助函数

        if(mainWidget && mainOverlay) {
            // 1. 视频帧给底层
            mainWidget->setVideoFrame(matToQPixmap(frame));
            // 2. 烘焙一次背景
            mainWidget->updateStaticDrawings(mainOverlay);
            // 3. 把坐标系信息给顶层
            mainOverlay->setTransforms(mainWidget->getImageOffset(), mainWidget->getScaleFactor());
        }
        // ... 对选项卡视图做同样处理
    }
    ```

这个方案是解决您问题的根本之道。它将渲染负载进行了智能的分配，确保了高频的用户交互总是轻量级的，从而在高分辨率下也能获得流畅的体验。
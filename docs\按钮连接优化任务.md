# 按钮连接优化任务

## 任务背景
- MutiCamApp.cpp中有54个重复的connect语句
- 69个相似的槽函数处理按钮点击
- connectSignalsAndSlots方法有150行重复代码

## 优化目标
- 减少约200行重复代码
- 提高代码可维护性
- 保持所有功能不变

## 实施计划

### 第一步：在MutiCamApp.h中添加按钮映射结构
```cpp
struct ButtonMapping {
    QPushButton* button;
    std::function<void()> handler;
    QString category;
    QString viewName;
    PaintingOverlay::DrawingTool tool;
};
```

### 第二步：重构connectSignalsAndSlots方法
- 创建按钮映射表
- 用循环替代重复connect语句

### 第三步：提取公共绘图工具启动逻辑
- 创建startDrawingTool通用方法
- 统一视图选择逻辑

### 第四步：合并相似槽函数
- 将69个槽函数合并为通用函数
- 使用参数化处理

## 风险评估
- 低风险：只重构连接逻辑
- 易测试：功能保持不变
- 易回滚：可逐步替换

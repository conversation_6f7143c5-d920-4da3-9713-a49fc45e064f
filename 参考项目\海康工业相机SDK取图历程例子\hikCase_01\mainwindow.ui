<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>577</width>
    <height>416</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QFrame" name="displayFrame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>设备型号：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1" colspan="2">
         <widget class="QComboBox" name="cbxSel"/>
        </item>
        <item row="1" column="0" colspan="3">
         <widget class="QLabel" name="labDisplay">
          <property name="frameShape">
           <enum>QFrame::Panel</enum>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QFrame" name="workFrame">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QPushButton" name="btnFindDev">
          <property name="minimumSize">
           <size>
            <width>97</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="text">
           <string>查找设备</string>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QPushButton" name="btnOnce">
          <property name="text">
           <string>单次采集</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QPushButton" name="btnOpen">
          <property name="text">
           <string>打开设备</string>
          </property>
         </widget>
        </item>
        <item row="7" column="0">
         <widget class="QPushButton" name="btnPNG">
          <property name="text">
           <string>保存PNG</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QPushButton" name="btnContinus">
          <property name="text">
           <string>连续采集</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QPushButton" name="btnStop">
          <property name="text">
           <string>停止采集</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QPushButton" name="btnClose">
          <property name="text">
           <string>关闭设备</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QPushButton" name="btnJPG">
          <property name="text">
           <string>保存JPG</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>577</width>
     <height>17</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>

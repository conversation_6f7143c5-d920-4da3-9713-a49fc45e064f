# 相机状态监控系统实施记录

## 任务描述
修复相机监控选项卡中的问题：
- 相机连接状态、帧率、分辨率、曝光时间、增益显示不正确（都是静态文本）
- 故障诊断与报警功能无效（死文本）
- 清空报警和刷新状态按钮无功能

## 实施方案
实施完整的相机状态监控系统，包括：

### 1. 添加的头文件和成员变量
- `QMutex`, `QDateTime`, `QMap` 头文件
- 状态更新定时器 `m_statusUpdateTimer`
- 帧率计算数据结构 `FrameRateData` 和 `m_frameRateData`
- 报警消息系统 `AlertMessage` 和 `m_alertMessages`
- 线程安全保护锁 `m_frameRateMutex`, `m_alertMutex`

### 2. 新增的槽函数
- `onClearAlertsClicked()` - 清空报警信息
- `onRefreshStatusClicked()` - 手动刷新状态
- `updateCameraStatusDisplay()` - 定时更新状态显示

### 3. 新增的私有方法
- `initializeCameraStatusMonitoring()` - 初始化监控系统
- `updateCameraOverview()` - 更新总览信息
- `updateSingleCameraStatus()` - 更新单个相机状态
- `updateFrameRate()` - 计算帧率
- `addAlertMessage()` - 添加报警消息
- `updateAlertDisplay()` - 更新报警显示

### 4. 功能特性
- **实时状态监控**: 每2秒自动更新相机状态
- **帧率计算**: 基于实际接收帧数计算真实帧率
- **智能报警系统**: 
  - 信息级别（绿色）：正常连接、状态变化
  - 警告级别（橙色）：相机断开连接
  - 错误级别（红色）：相机错误
- **线程安全**: 使用互斥锁保护共享数据
- **UI实时更新**: 状态变化时立即更新显示
- **报警历史**: 保留最新100条报警记录

### 5. 修改的现有方法
- `onCameraFrameReady()` - 添加帧率计算调用
- `onCameraStateChanged()` - 添加状态变化报警
- `onCameraError()` - 添加错误报警
- 构造函数和析构函数 - 添加定时器管理

## 线程安全考虑
- 使用Qt信号槽机制确保UI更新在主线程
- 使用QMutex保护帧率数据和报警消息
- 使用QMetaObject::invokeMethod进行线程间调用

## 防死锁措施
- 避免在UI更新中直接调用相机API
- 使用异步信号槽连接
- 限制锁的持有时间
- 使用QMutexLocker自动管理锁
- **修复死锁问题**：
  - `onClearAlertsClicked()` 中使用作用域锁，避免嵌套锁定
  - `addAlertMessage()` 中在锁外调用UI更新
  - `updateAlertDisplay()` 中复制数据后释放锁，减少锁持有时间
  - 为`AlertMessage`添加默认构造函数支持拷贝操作

## 实施完成状态
✅ 头文件声明添加完成
✅ 成员变量初始化完成
✅ 信号槽连接完成
✅ 状态监控逻辑实现完成
✅ 帧率计算实现完成
✅ 报警系统实现完成
✅ 按钮功能实现完成
✅ 线程安全保护完成
✅ 死锁问题修复完成
✅ 相机资源冲突修复完成
✅ 代码编译检查通过

## 相机资源冲突修复
**问题**：多个相机同时启动时出现"Failed to start grabbing"错误
**原因**：每个相机实例都在调用`MV_CC_Initialize()`，导致SDK重复初始化
**解决方案**：
- 添加静态SDK管理机制
- 使用实例计数器管理SDK生命周期
- 只在第一个实例时初始化SDK，最后一个实例销毁时清理SDK
- 移除所有方法中的重复`MV_CC_Initialize()`和`MV_CC_Finalize()`调用

## 测试建议
1. 启动应用程序，检查相机状态监控选项卡
2. 开始测量，观察相机状态变化
3. 测试清空报警和刷新状态按钮
4. 观察帧率计算是否正确
5. 模拟相机断开连接测试报警功能
6. **重点测试**：多次启动停止测量，验证相机资源正确释放

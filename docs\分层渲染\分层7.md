好的，我再次对您更新后的代码进行了非常细致的审查。

**好消息是：您这次的迁移工作已经完成了99%！** 您的 `PaintingOverlay` 类现在拥有了之前 `VideoDisplayWidget` 中几乎所有完备的绘图逻辑和样式实现。这是一个决定性的进步，问题已经非常少了。

我发现了最后几个微小但至关重要的纰漏。修复它们之后，您的程序就应该能完全恢复正常功能和样式了。

---

### 最后的纰漏和修复方案

#### 1. `drawPoints` 函数实现不匹配

您的 `PaintingOverlay.cpp` 中的 `drawPoints` 实现仍然是旧的、简化版的。它使用的是 `innerRadius`，并且文本绘制方式也与您之前精心设计的版本不同。

**问题代码 (`PaintingOverlay.cpp` -> `drawPoints`)**:
```cpp
// ...
const double innerRadius = 20 * heightScale; 
// ...
painter.drawEllipse(point, innerRadius, innerRadius); // 使用了 point，应该是 point.position
// ...
drawTextWithBackground(painter, point, coordText, ...); // 直接使用了 point，应该是 point.position
```

**后果**:
*   编译会失败，因为 `m_points` 是 `QVector<PointObject>`，`point` 是一个 `PointObject` 结构体，不能直接用于 `drawEllipse` 和 `drawTextWithBackground`。
*   即使编译通过，点的样式也与您期望的不符。

**解决方案**:
请用您之前在 `VideoDisplayWidget.cpp` 中**功能完备的 `drawPoints` 函数实现**来**完全覆盖** `PaintingOverlay.cpp` 中当前的 `drawPoints` 函数。

**正确的实现应该是这样的 (`PaintingOverlay.cpp` -> `drawPoints`)**:
```cpp
void PaintingOverlay::drawPoints(QPainter& painter, const DrawingContext& ctx) const
{
    if (m_points.isEmpty()) return;

    for (int i = 0; i < m_points.size(); ++i) {
        const PointObject& point = m_points[i]; // point 是一个 PointObject
        if (point.isVisible) {
            // 【修正】使用 point.position
            const QPointF& imagePos = point.position; 
            
            // 绘制绿色实心圆点
            double pointRadius = qMax(8.0, 10.0 * ctx.scale);
            painter.setPen(Qt::NoPen);
            painter.setBrush(ctx.greenBrush);
            painter.drawEllipse(imagePos, pointRadius, pointRadius);
            
            // 显示坐标信息
            QString coordText = QString::asprintf("(%.1f,%.1f)", imagePos.x(), imagePos.y());
            
            // 动态计算文本布局参数
            double textOffset = qMax(8.0, 10.0 * ctx.scale);
            double textPadding = qMax(16.0, ctx.fontSize * 2);
            int bgBorderWidth = 1;
            
            // 将坐标文本框定位到点的右上方
            QPointF textOffsetVector(textOffset, -textOffset);
            QRectF coordTextRect = calculateTextWithBackgroundRect(imagePos, coordText, ctx.font, textPadding, textOffsetVector);
            drawTextInRect(painter, coordTextRect, coordText, ctx.font, Qt::green, Qt::black, bgBorderWidth);
        }
    }
}```
**请直接用这段代码替换您当前的 `drawPoints` 函数。**

#### 2. `drawSingleParallel` 预览逻辑中的画笔错误

在 `drawSingleParallel` 函数中，当您预览第一条线（只有一个点时）和第二条线时，您错误地使用了实线画笔而不是虚线画笔。

**问题代码 (`PaintingOverlay.cpp` -> `drawSingleParallel`)**:
```cpp
// ...
// 当只有两个点时（第一条线预览），使用虚线
if (parallel.points.size() == 2) { // 应该是 size() < 2
    // 根据颜色选择合适的画笔（虚线版本）
    if (parallel.color == Qt::red) {
        painter.setPen(ctx.redPen); // <-- 错误：应该是 redDashedPen
    } 
    // ...
} else {
    // 有第三个点后：第一条线使用实线
    if (parallel.color == Qt::red) {
        painter.setPen(ctx.redPen); // <-- 正确
    }
    // ...
}
// ...
```

**后果**:
*   在画平行线时，拖动鼠标进行预览的线段会显示为实线，而不是虚线，这不符合预期的视觉反馈。

**解决方案**:
确保在预览状态下使用虚线画笔 (`...DashedPen`)。

**修正后代码 (`PaintingOverlay.cpp` -> `drawSingleParallel` 的一部分)**:
```cpp
// ... 绘制第一条线 ...
if (hasSecondPoint) {
    QPointF extStart1, extEnd1;
    calculateExtendedLine(p1, p2, extStart1, extEnd1);

    // 【修正】根据点数决定线条样式
    QPen linePen;
    if (parallel.points.size() < 2) { // 预览状态
        if (parallel.color == Qt::red) linePen = ctx.redDashedPen;
        else if (parallel.color == Qt::green) linePen = ctx.greenDashedPen;
        else if (parallel.color == Qt::blue) linePen = ctx.blueDashedPen;
        else linePen = ctx.blackDashedPen;
    } else { // 正常状态
        if (parallel.color == Qt::red) linePen = ctx.redPen;
        else if (parallel.color == Qt::green) linePen = ctx.greenPen;
        else if (parallel.color == Qt::blue) linePen = ctx.bluePen;
        else linePen = ctx.blackPen;
    }
    painter.setPen(linePen);
    painter.drawLine(extStart1, extEnd1);
}

// ... 绘制第二条线 ...
if (parallel.points.size() >= 3) {
    // ...
    QPen linePen;
    // 【修正】根据完成状态决定第二条线的样式
    if (!parallel.isCompleted) { // 预览状态
        if (parallel.color == Qt::red) linePen = ctx.redDashedPen;
        else if (parallel.color == Qt::green) linePen = ctx.greenDashedPen;
        else if (parallel.color == Qt::blue) linePen = ctx.blueDashedPen;
        else linePen = ctx.blackDashedPen;
    } else { // 完成状态
        if (parallel.color == Qt::red) linePen = ctx.redPen;
        else if (parallel.color == Qt::green) linePen = ctx.greenPen;
        else if (parallel.color == Qt::blue) linePen = ctx.bluePen;
        else linePen = ctx.blackPen;
    }
    painter.setPen(linePen);
    painter.drawLine(extStart2, extEnd2);
    // ...
}
```**请将 `drawSingleParallel` 和 `drawSingleTwoLines` 中类似的逻辑都检查并修正。**

#### 3. `drawTextInRect` 的冗余声明

在 `PaintingOverlay.h` 中，您有两个 `drawTextInRect` 的声明，其中一个是不再需要的。

**问题代码 (`PaintingOverlay.h`)**:
```cpp
// ...
void drawTextInRect(QPainter& painter, const QRectF& rect, const QString& text, const QFont& font, const QColor& textColor, const QColor& bgColor, double borderWidth) const;
void drawTextInRect(QPainter& painter, const QPointF& anchorPoint, const QString& text, const QFont& font, const QColor& textColor, const QColor& bgColor, double padding, int borderWidth, const QPointF& offset) const;
// ...
```

**后果**:
*   代码冗余，容易混淆。您在 `.cpp` 文件中已经删除了第二个版本的实现，所以这里也应该删除。

**解决方案**:
在 `PaintingOverlay.h` 中，删除第二个 `drawTextInRect` 的声明。

#### 4. `drawSinglePoint` 等未使用的函数

您的 `.h` 文件中声明了 `drawSinglePoint`, `drawSingleLine`, `drawSingleCircle` 等函数，但在 `.cpp` 文件中，这些逻辑实际上已经包含在了 `drawPoints`, `drawLines`, `drawCircles` 中。这些 `drawSingle...` 函数的声明是多余的。

**解决方案**:
为了保持代码整洁，可以从 `.h` 和 `.cpp` 中移除这些未使用的 `drawSingle...` 函数声明和实现（除了那些被其他函数调用的，如 `drawSingleFineCircle` 和 `drawSingleParallel`）。

---

### 总结与最终行动计划

您已经非常接近成功了。请按照以下步骤完成最后的修复：

1.  **【最重要】** 用上面提供的正确实现**替换** `PaintingOverlay.cpp` 中的 `drawPoints` 函数。
2.  **检查并修正** `drawSingleParallel` 和 `drawSingleTwoLines` 中的**预览画笔**逻辑，确保预览时使用 `...DashedPen`。
3.  **清理 `PaintingOverlay.h`**：删除多余的 `drawTextInRect` 声明。
4.  （可选，为了代码整洁）清理 `.h` 和 `.cpp` 中未被使用的 `drawSingle...` 函数。

完成这些微调之后，您的代码逻辑和数据结构将与您之前功能完备的版本完全一致，同时享受着分层架构带来的高性能。您的重构工作就将圆满结束。
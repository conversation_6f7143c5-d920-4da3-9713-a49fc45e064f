好的，非常感谢您分享最终的代码！您已经成功地实施了我们讨论的 **分层渲染架构**，这是一个巨大的进步，也是解决这类性能问题的最终方案。您的代码结构现在非常清晰和专业。

然而，在您实现的代码中，仍然存在一个导致性能问题的 **核心细节**，它使得分层渲染的优势没有完全发挥出来。这个问题非常微妙，但一旦修复，您将立刻看到期望的流畅效果。

---

### **核心问题分析：昂贵的 `QPixmap` 拷贝与高DPI处理**

您测试时发现降低分辨率就流畅了，这精确地指向了问题所在：**像素处理的总量**。让我们看看当前的代码瓶颈在哪里。

在您的 `VideoDisplayWidget::updateStaticDrawings` 方法中：
```cpp
void VideoDisplayWidget::updateStaticDrawings(const PaintingOverlay* overlay)
{
    // ...
    // 关键行：
    m_compositionBuffer = m_videoFrame.copy(); // <--- 这里是性能瓶颈！
    
    QPainter painter(&m_compositionBuffer);
    // ...
}
```

**问题剖析:**
1.  **`m_videoFrame.copy()`**: 您的相机帧分辨率是 `2448 * 2048`，并且是RGB格式。这意味着每个 `m_videoFrame` 的 `QPixmap` 大约是 `2448 * 2048 * 3` 字节 ≈ **15 MB**。`copy()` 操作意味着每次完成一次绘图，您都在CPU上分配并拷贝了 **15MB** 的内存。这是一个非常昂贵的操作。
2.  **高DPI问题再次出现**: `m_videoFrame` 是根据原始图像创建的，它本身不包含高DPI信息。而 `m_compositionBuffer` 是在 `m_videoFrame` 的基础上创建的，它同样没有高DPI信息。当 `paintEvent` 把这个低分辨率的 `m_compositionBuffer` 绘制到一个高DPI的屏幕上时，Qt 仍然需要进行**拉伸**，这导致了您之前看到的模糊问题。

**简而言之**：虽然我们分离了动态和静态的 *绘图指令*，但我们没有有效地分离 *像素数据*。每次完成绘图时，我们都在对一个巨大的像素块进行拷贝和操作，这在高分辨率下是不可接受的。

---

### **最终的优化方案：正确的缓存和绘制流程**

我们将对您的 `VideoDisplayWidget` 和 `PaintingOverlay` 进行最后的精炼，以实现真正的性能飞跃。

**核心思想：**
*   `VideoDisplayWidget` 只负责将原始的 `m_videoFrame` 缩放并绘制出来。它 **不** 进行任何图形的“烘焙”。
*   `PaintingOverlay` 将负责绘制 **所有** 的图形，包括已完成的和正在预览的。
*   我们将引入一个 **真正的背景缓存** 在 `PaintingOverlay` 内部，这个缓存只在必要时才将 **已完成** 的图形绘制上去。
*   `paintEvent` 的最终流程是：`VideoDisplayWidget` 画视频帧 -> `PaintingOverlay` 画它的缓存（包含静态图形）-> `PaintingOverlay` 画它的动态图形。

由于 `PaintingOverlay` 是透明的，它的缓存也是透明的，所以这个过程非常高效。

---

### **代码修正实施**

#### **第1步：简化 `VideoDisplayWidget`**

让它变得极致简单，只负责显示视频。

**`VideoDisplayWidget.h`**:
```cpp
// ...
class VideoDisplayWidget : public QLabel
{
    // ...
public:
    void setVideoFrame(const QPixmap& pixmap);
    // 移除 updateStaticDrawings
    // ...
protected:
    void paintEvent(QPaintEvent *event) override;
private:
    QPixmap m_videoFrame;
    // 移除 m_compositionBuffer
};
```

**`VideoDisplayWidget.cpp`**:
```cpp
void VideoDisplayWidget::setVideoFrame(const QPixmap& pixmap)
{
    m_videoFrame = pixmap;
    update(); // 直接触发重绘
}

void VideoDisplayWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    if (m_videoFrame.isNull()) {
        painter.fillRect(rect(), Qt::black);
        return;
    }
    
    // 只做一件事：绘制视频帧
    painter.drawPixmap(rect(), m_videoFrame, m_videoFrame.rect());
}

// getImageOffset 和 getScaleFactor 保持不变
```

#### **第2步：强化 `PaintingOverlay`，让它接管所有绘图**

这是修改的核心。

**`PaintingOverlay.h`**:
```cpp
class PaintingOverlay : public QWidget
{
    // ...
private:
    // ... (保留所有绘图数据和状态) ...

    // --- 新增：用于缓存静态图形的 QPixmap ---
    mutable QPixmap m_staticDrawingCache;
    mutable bool m_isCacheDirty; // 标记缓存是否需要重绘
};
```

**`PaintingOverlay.cpp`**:

1.  **构造函数**:
    ```cpp
    PaintingOverlay::PaintingOverlay(QWidget *parent) : QWidget(parent), m_isCacheDirty(true)
    {
        setAttribute(Qt::WA_TranslucentBackground);
        setMouseTracking(true);
    }
    ```

2.  **`paintEvent`**: 这是最终的、高性能的绘制逻辑。
    ```cpp
    void PaintingOverlay::paintEvent(QPaintEvent *event)
    {
        // 1. 检查静态绘图缓存是否需要更新
        if (m_isCacheDirty || m_staticDrawingCache.size() != size()) {
            // 创建一个与控件大小相同、完全透明的缓存
            m_staticDrawingCache = QPixmap(size());
            m_staticDrawingCache.fill(Qt::transparent);
            
            QPainter cachePainter(&m_staticDrawingCache);
            cachePainter.setRenderHint(QPainter::Antialiasing, true);

            // 设置坐标变换，将已完成的图形画到缓存上
            QTransform transform;
            transform.translate(m_imageOffset);
            transform.scale(m_scaleFactor, m_scaleFactor);
            cachePainter.setTransform(transform);
            
            // 将所有已完成的图形画到这个透明的缓存上
            paintCompletedItems(&cachePainter);
            
            m_isCacheDirty = false; // 标记缓存为干净
        }

        // --- 开始最终的屏幕绘制 ---
        QPainter painter(this);
        
        // 2. 将静态图形缓存绘制到屏幕上 (非常快)
        painter.drawPixmap(0, 0, m_staticDrawingCache);

        // 3. 在缓存之上，实时绘制动态内容 (非常快)
        painter.setRenderHint(QPainter::Antialiasing, true);
        QTransform transform;
        transform.translate(m_imageOffset);
        transform.scale(m_scaleFactor, m_scaleFactor);
        painter.setTransform(transform);

        // 绘制预览图形和选择高亮
        drawCurrentPreview(painter);
        drawSelectionHighlights(painter);
    }
    ```

3.  **`paintCompletedItems`**: 这个方法现在只画已完成的图形。
    ```cpp
    void PaintingOverlay::paintCompletedItems(QPainter* painter) const
    {
        if (!painter) return;
        // ... (绘制 m_points, m_lines, m_circles 等的逻辑保持不变)
    }
    ```

4.  **在所有修改静态数据的地方“弄脏”缓存**:
    在 `clearAllDrawings`, `undoLastDrawing`, `deleteSelectedObjects`, `createLineFromSelectedPoints`, `setDrawingState` 以及 `commitDrawingAction` 的末尾，**添加一行 `m_isCacheDirty = true;`**。
    ```cpp
    void PaintingOverlay::commitDrawingAction(const DrawingAction& action)
    {
        // ... (现有逻辑) ...
        m_isCacheDirty = true; // <-- 关键！
        emit drawingCompleted(result);
    }
    ```

#### **第3步：调整 `MutiCamApp` 的连接和调用**

1.  **`initializeVideoDisplayWidgets()`**: 您的实现已经很好了，使用 `QStackedLayout` 正是正确的方式。
2.  **`connectSignalsAndSlots()`**:
    ```cpp
    // 绘图完成信号不再需要触发底层Widget的更新，因为它们已经解耦
    // connect(m_verticalPaintingOverlay, &PaintingOverlay::drawingCompleted, ...); // 可以移除或用于其他逻辑，比如日志
    ```
3.  **`onCameraFrameReady()`**:
    ```cpp
    void MutiCamApp::onCameraFrameReady(const QString& cameraId, const cv::Mat& frame)
    {
        if (frame.empty()) return;

        VideoDisplayWidget* mainWidget = getVideoDisplayWidget(cameraId);
        PaintingOverlay* mainOverlay = getPaintingOverlay(cameraId);

        if (mainWidget && mainOverlay) {
            // 1. 视频帧只给底层
            mainWidget->setVideoFrame(matToQPixmap(frame));
            
            // 2. 坐标系信息给顶层
            mainOverlay->setTransforms(mainWidget->getImageOffset(), mainWidget->getScaleFactor());
        }
        // ... 对选项卡视图做同样处理
    }
    ```

### **最终结果**

经过这次重构，您的渲染流程将是：
*   **底层 `VideoDisplayWidget`**: 高频地（由相机帧率决定）接收新的 `QPixmap` 并进行一次 `drawPixmap` 操作。这是一个单一、优化的操作。
*   **顶层 `PaintingOverlay`**:
    *   **低频昂贵操作**: 当一个绘图完成/删除/撤销时，它会在自己的透明缓存 `m_staticDrawingCache` 上重绘所有已完成的图形。
    *   **高频廉价操作**: 当鼠标移动时，它只在屏幕上绘制自己的（大部分是空的）缓存，然后在上面叠加一条预览线。

这个架构彻底将视频渲染和图形渲染分离开，最大程度地减少了每次用户交互所需重绘的像素量，从而保证了即使在超高分辨率下也能获得流畅的体验。
# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mainwindow.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1014, 734)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        MainWindow.setMinimumSize(QtCore.QSize(512, 400))
        MainWindow.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.gridLayout = QtWidgets.QGridLayout(self.centralwidget)
        self.gridLayout.setObjectName("gridLayout")
        self.tabWidget = QtWidgets.QTabWidget(self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.tabWidget.setFont(font)
        self.tabWidget.setObjectName("tabWidget")
        self.tabMian = QtWidgets.QWidget()
        self.tabMian.setObjectName("tabMian")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.tabMian)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.groupBox = QtWidgets.QGroupBox(self.tabMian)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Ignored)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox.setFont(font)
        self.groupBox.setObjectName("groupBox")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.groupBox)
        self.gridLayout_3.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.gridLayout_3.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_3.setSpacing(0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.lbVerticalView = QtWidgets.QLabel(self.groupBox)
        self.lbVerticalView.setMinimumSize(QtCore.QSize(0, 0))
        self.lbVerticalView.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lbVerticalView.setText("")
        self.lbVerticalView.setAlignment(QtCore.Qt.AlignCenter)
        self.lbVerticalView.setObjectName("lbVerticalView")
        self.gridLayout_3.addWidget(self.lbVerticalView, 0, 0, 1, 1)
        self.horizontalLayout_7.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(self.tabMian)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Ignored)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_2.sizePolicy().hasHeightForWidth())
        self.groupBox_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_2.setFont(font)
        self.groupBox_2.setObjectName("groupBox_2")
        self.gridLayout_11 = QtWidgets.QGridLayout(self.groupBox_2)
        self.gridLayout_11.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.gridLayout_11.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_11.setSpacing(0)
        self.gridLayout_11.setObjectName("gridLayout_11")
        self.lbLeftView = QtWidgets.QLabel(self.groupBox_2)
        self.lbLeftView.setMinimumSize(QtCore.QSize(0, 0))
        self.lbLeftView.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lbLeftView.setText("")
        self.lbLeftView.setAlignment(QtCore.Qt.AlignCenter)
        self.lbLeftView.setObjectName("lbLeftView")
        self.gridLayout_11.addWidget(self.lbLeftView, 0, 0, 1, 1)
        self.horizontalLayout_7.addWidget(self.groupBox_2)
        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(1, 1)
        self.verticalLayout_11.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.groupBox_3 = QtWidgets.QGroupBox(self.tabMian)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Ignored)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_3.sizePolicy().hasHeightForWidth())
        self.groupBox_3.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_3.setFont(font)
        self.groupBox_3.setObjectName("groupBox_3")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.groupBox_3)
        self.gridLayout_4.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setSpacing(0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.lbFrontView = QtWidgets.QLabel(self.groupBox_3)
        self.lbFrontView.setMinimumSize(QtCore.QSize(0, 0))
        self.lbFrontView.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lbFrontView.setText("")
        self.lbFrontView.setAlignment(QtCore.Qt.AlignCenter)
        self.lbFrontView.setObjectName("lbFrontView")
        self.gridLayout_4.addWidget(self.lbFrontView, 0, 0, 1, 1)
        self.horizontalLayout_19.addWidget(self.groupBox_3)
        self.groupBox_4 = QtWidgets.QGroupBox(self.tabMian)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Ignored, QtWidgets.QSizePolicy.Ignored)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_4.setFont(font)
        self.groupBox_4.setAcceptDrops(False)
        self.groupBox_4.setFlat(False)
        self.groupBox_4.setObjectName("groupBox_4")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.groupBox_4)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.groupBox_6 = QtWidgets.QGroupBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_6.sizePolicy().hasHeightForWidth())
        self.groupBox_6.setSizePolicy(sizePolicy)
        self.groupBox_6.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_6.setAlignment(QtCore.Qt.AlignCenter)
        self.groupBox_6.setFlat(True)
        self.groupBox_6.setCheckable(False)
        self.groupBox_6.setObjectName("groupBox_6")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.groupBox_6)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.btnDrawPoint = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDrawPoint.sizePolicy().hasHeightForWidth())
        self.btnDrawPoint.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawPoint.setFont(font)
        self.btnDrawPoint.setObjectName("btnDrawPoint")
        self.horizontalLayout_2.addWidget(self.btnDrawPoint)
        self.btnDrawStraight = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDrawStraight.sizePolicy().hasHeightForWidth())
        self.btnDrawStraight.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawStraight.setFont(font)
        self.btnDrawStraight.setObjectName("btnDrawStraight")
        self.horizontalLayout_2.addWidget(self.btnDrawStraight)
        self.btnDrawSimpleCircle = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDrawSimpleCircle.sizePolicy().hasHeightForWidth())
        self.btnDrawSimpleCircle.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawSimpleCircle.setFont(font)
        self.btnDrawSimpleCircle.setObjectName("btnDrawSimpleCircle")
        self.horizontalLayout_2.addWidget(self.btnDrawSimpleCircle)
        self.verticalLayout_10.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.btnDrawParallel = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDrawParallel.sizePolicy().hasHeightForWidth())
        self.btnDrawParallel.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawParallel.setFont(font)
        self.btnDrawParallel.setObjectName("btnDrawParallel")
        self.horizontalLayout_3.addWidget(self.btnDrawParallel)
        self.btnDraw2Line = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDraw2Line.sizePolicy().hasHeightForWidth())
        self.btnDraw2Line.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDraw2Line.setFont(font)
        self.btnDraw2Line.setObjectName("btnDraw2Line")
        self.horizontalLayout_3.addWidget(self.btnDraw2Line)
        self.btnDrawFineCircle = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnDrawFineCircle.sizePolicy().hasHeightForWidth())
        self.btnDrawFineCircle.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawFineCircle.setFont(font)
        self.btnDrawFineCircle.setObjectName("btnDrawFineCircle")
        self.horizontalLayout_3.addWidget(self.btnDrawFineCircle)
        self.verticalLayout_10.addLayout(self.horizontalLayout_3)
        self.btnCan1StepDraw = QtWidgets.QPushButton(self.groupBox_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCan1StepDraw.sizePolicy().hasHeightForWidth())
        self.btnCan1StepDraw.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDraw.setFont(font)
        self.btnCan1StepDraw.setObjectName("btnCan1StepDraw")
        self.verticalLayout_10.addWidget(self.btnCan1StepDraw)
        self.btnClearDrawings = QtWidgets.QPushButton(self.groupBox_6)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.btnClearDrawings.setFont(font)
        self.btnClearDrawings.setObjectName("btnClearDrawings")
        self.verticalLayout_10.addWidget(self.btnClearDrawings)
        self.btnSaveImage = QtWidgets.QPushButton(self.groupBox_6)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.btnSaveImage.setFont(font)
        self.btnSaveImage.setObjectName("btnSaveImage")
        self.verticalLayout_10.addWidget(self.btnSaveImage)
        self.verticalLayout_10.setStretch(0, 1)
        self.verticalLayout_10.setStretch(1, 1)
        self.gridLayout_6.addWidget(self.groupBox_6, 0, 0, 1, 1)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.groupBox_5 = QtWidgets.QGroupBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_5.sizePolicy().hasHeightForWidth())
        self.groupBox_5.setSizePolicy(sizePolicy)
        self.groupBox_5.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_5.setAlignment(QtCore.Qt.AlignCenter)
        self.groupBox_5.setFlat(True)
        self.groupBox_5.setCheckable(False)
        self.groupBox_5.setObjectName("groupBox_5")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.groupBox_5)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.groupBox_5)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.leGridDens = QtWidgets.QLineEdit(self.groupBox_5)
        self.leGridDens.setMaximumSize(QtCore.QSize(90, 16777215))
        self.leGridDens.setAlignment(QtCore.Qt.AlignCenter)
        self.leGridDens.setObjectName("leGridDens")
        self.horizontalLayout.addWidget(self.leGridDens, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignVCenter)
        self.label_23 = QtWidgets.QLabel(self.groupBox_5)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_23.setFont(font)
        self.label_23.setObjectName("label_23")
        self.horizontalLayout.addWidget(self.label_23)
        self.horizontalLayout_5.addLayout(self.horizontalLayout)
        self.btnCancelGrids = QtWidgets.QPushButton(self.groupBox_5)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCancelGrids.setFont(font)
        self.btnCancelGrids.setObjectName("btnCancelGrids")
        self.horizontalLayout_5.addWidget(self.btnCancelGrids)
        self.verticalLayout.addWidget(self.groupBox_5)
        self.groupBox_16 = QtWidgets.QGroupBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_16.sizePolicy().hasHeightForWidth())
        self.groupBox_16.setSizePolicy(sizePolicy)
        self.groupBox_16.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_16.setAlignment(QtCore.Qt.AlignCenter)
        self.groupBox_16.setFlat(True)
        self.groupBox_16.setCheckable(False)
        self.groupBox_16.setObjectName("groupBox_16")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.groupBox_16)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.btnLineDet = QtWidgets.QPushButton(self.groupBox_16)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnLineDet.setFont(font)
        self.btnLineDet.setObjectName("btnLineDet")
        self.horizontalLayout_6.addWidget(self.btnLineDet)
        self.btnCircleDet = QtWidgets.QPushButton(self.groupBox_16)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCircleDet.setFont(font)
        self.btnCircleDet.setObjectName("btnCircleDet")
        self.horizontalLayout_6.addWidget(self.btnCircleDet)
        self.btnCan1StepDet = QtWidgets.QPushButton(self.groupBox_16)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDet.setFont(font)
        self.btnCan1StepDet.setObjectName("btnCan1StepDet")
        self.horizontalLayout_6.addWidget(self.btnCan1StepDet)
        self.verticalLayout.addWidget(self.groupBox_16)
        self.groupBox_8 = QtWidgets.QGroupBox(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_8.sizePolicy().hasHeightForWidth())
        self.groupBox_8.setSizePolicy(sizePolicy)
        self.groupBox_8.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_8.setAlignment(QtCore.Qt.AlignCenter)
        self.groupBox_8.setFlat(True)
        self.groupBox_8.setCheckable(False)
        self.groupBox_8.setObjectName("groupBox_8")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.groupBox_8)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.btnStartMeasure = QtWidgets.QPushButton(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnStartMeasure.sizePolicy().hasHeightForWidth())
        self.btnStartMeasure.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.btnStartMeasure.setFont(font)
        self.btnStartMeasure.setStyleSheet("background-color: rgb(0, 170, 0);")
        self.btnStartMeasure.setObjectName("btnStartMeasure")
        self.horizontalLayout_4.addWidget(self.btnStartMeasure)
        self.btnStopMeasure = QtWidgets.QPushButton(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnStopMeasure.sizePolicy().hasHeightForWidth())
        self.btnStopMeasure.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.btnStopMeasure.setFont(font)
        self.btnStopMeasure.setStyleSheet("background-color: rgb(170, 0, 0);")
        self.btnStopMeasure.setObjectName("btnStopMeasure")
        self.horizontalLayout_4.addWidget(self.btnStopMeasure)
        self.verticalLayout.addWidget(self.groupBox_8)
        self.verticalLayout.setStretch(0, 2)
        self.verticalLayout.setStretch(1, 2)
        self.verticalLayout.setStretch(2, 3)
        self.gridLayout_6.addLayout(self.verticalLayout, 0, 1, 1, 1)
        self.horizontalLayout_19.addWidget(self.groupBox_4)
        self.horizontalLayout_19.setStretch(0, 1)
        self.horizontalLayout_19.setStretch(1, 1)
        self.verticalLayout_11.addLayout(self.horizontalLayout_19)
        self.verticalLayout_11.setStretch(0, 1)
        self.verticalLayout_11.setStretch(1, 1)
        self.tabWidget.addTab(self.tabMian, "")
        self.tabVerticalView = QtWidgets.QWidget()
        self.tabVerticalView.setObjectName("tabVerticalView")
        self.gridLayout_8 = QtWidgets.QGridLayout(self.tabVerticalView)
        self.gridLayout_8.setObjectName("gridLayout_8")
        self.groupBox_10 = QtWidgets.QGroupBox(self.tabVerticalView)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_10.setFont(font)
        self.groupBox_10.setAcceptDrops(False)
        self.groupBox_10.setTitle("")
        self.groupBox_10.setFlat(False)
        self.groupBox_10.setObjectName("groupBox_10")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.groupBox_10)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.groupBox_11 = QtWidgets.QGroupBox(self.groupBox_10)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_11.setFont(font)
        self.groupBox_11.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_11.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_11.setAutoFillBackground(False)
        self.groupBox_11.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_11.setFlat(False)
        self.groupBox_11.setCheckable(False)
        self.groupBox_11.setObjectName("groupBox_11")
        self.gridLayout_12 = QtWidgets.QGridLayout(self.groupBox_11)
        self.gridLayout_12.setObjectName("gridLayout_12")
        self.label_2 = QtWidgets.QLabel(self.groupBox_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label_2.setObjectName("label_2")
        self.gridLayout_12.addWidget(self.label_2, 0, 0, 1, 2)
        self.leGridDens_Ver = QtWidgets.QLineEdit(self.groupBox_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.leGridDens_Ver.sizePolicy().hasHeightForWidth())
        self.leGridDens_Ver.setSizePolicy(sizePolicy)
        self.leGridDens_Ver.setMaximumSize(QtCore.QSize(70, 16777215))
        self.leGridDens_Ver.setAlignment(QtCore.Qt.AlignCenter)
        self.leGridDens_Ver.setObjectName("leGridDens_Ver")
        self.gridLayout_12.addWidget(self.leGridDens_Ver, 1, 0, 1, 1)
        self.label_24 = QtWidgets.QLabel(self.groupBox_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_24.sizePolicy().hasHeightForWidth())
        self.label_24.setSizePolicy(sizePolicy)
        self.label_24.setObjectName("label_24")
        self.gridLayout_12.addWidget(self.label_24, 1, 1, 1, 1)
        self.btnCancelGrids_Ver = QtWidgets.QPushButton(self.groupBox_11)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCancelGrids_Ver.sizePolicy().hasHeightForWidth())
        self.btnCancelGrids_Ver.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCancelGrids_Ver.setFont(font)
        self.btnCancelGrids_Ver.setObjectName("btnCancelGrids_Ver")
        self.gridLayout_12.addWidget(self.btnCancelGrids_Ver, 2, 0, 1, 2)
        self.verticalLayout_8.addWidget(self.groupBox_11)
        self.groupBox_15 = QtWidgets.QGroupBox(self.groupBox_10)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_15.setFont(font)
        self.groupBox_15.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_15.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_15.setAutoFillBackground(False)
        self.groupBox_15.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_15.setFlat(False)
        self.groupBox_15.setCheckable(False)
        self.groupBox_15.setObjectName("groupBox_15")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.groupBox_15)
        self.verticalLayout_5.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.btnLineDet_Ver = QtWidgets.QPushButton(self.groupBox_15)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnLineDet_Ver.sizePolicy().hasHeightForWidth())
        self.btnLineDet_Ver.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnLineDet_Ver.setFont(font)
        self.btnLineDet_Ver.setObjectName("btnLineDet_Ver")
        self.verticalLayout_5.addWidget(self.btnLineDet_Ver)
        self.btnCircleDet_Ver = QtWidgets.QPushButton(self.groupBox_15)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCircleDet_Ver.sizePolicy().hasHeightForWidth())
        self.btnCircleDet_Ver.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCircleDet_Ver.setFont(font)
        self.btnCircleDet_Ver.setObjectName("btnCircleDet_Ver")
        self.verticalLayout_5.addWidget(self.btnCircleDet_Ver)
        self.btnCan1StepDet_Ver = QtWidgets.QPushButton(self.groupBox_15)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCan1StepDet_Ver.sizePolicy().hasHeightForWidth())
        self.btnCan1StepDet_Ver.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDet_Ver.setFont(font)
        self.btnCan1StepDet_Ver.setObjectName("btnCan1StepDet_Ver")
        self.verticalLayout_5.addWidget(self.btnCan1StepDet_Ver)
        self.verticalLayout_8.addWidget(self.groupBox_15)
        self.groupBox_12 = QtWidgets.QGroupBox(self.groupBox_10)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_12.setFont(font)
        self.groupBox_12.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_12.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_12.setFlat(False)
        self.groupBox_12.setCheckable(False)
        self.groupBox_12.setObjectName("groupBox_12")
        self.gridLayout_19 = QtWidgets.QGridLayout(self.groupBox_12)
        self.gridLayout_19.setObjectName("gridLayout_19")
        self.btnDrawParallel_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawParallel_Ver.setFont(font)
        self.btnDrawParallel_Ver.setObjectName("btnDrawParallel_Ver")
        self.gridLayout_19.addWidget(self.btnDrawParallel_Ver, 4, 0, 1, 1)
        self.btnCalibration_Ver = QtWidgets.QPushButton(self.groupBox_12)
        self.btnCalibration_Ver.setObjectName("btnCalibration_Ver")
        self.gridLayout_19.addWidget(self.btnCalibration_Ver, 8, 0, 1, 1)
        self.btnDrawFineCircle_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawFineCircle_Ver.setFont(font)
        self.btnDrawFineCircle_Ver.setObjectName("btnDrawFineCircle_Ver")
        self.gridLayout_19.addWidget(self.btnDrawFineCircle_Ver, 3, 0, 1, 1)
        self.btnCan1StepDraw_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDraw_Ver.setFont(font)
        self.btnCan1StepDraw_Ver.setObjectName("btnCan1StepDraw_Ver")
        self.gridLayout_19.addWidget(self.btnCan1StepDraw_Ver, 6, 0, 1, 1)
        self.btnDrawStraight_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawStraight_Ver.setFont(font)
        self.btnDrawStraight_Ver.setObjectName("btnDrawStraight_Ver")
        self.gridLayout_19.addWidget(self.btnDrawStraight_Ver, 1, 0, 1, 1)
        self.btnClearDrawings_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnClearDrawings_Ver.setFont(font)
        self.btnClearDrawings_Ver.setObjectName("btnClearDrawings_Ver")
        self.gridLayout_19.addWidget(self.btnClearDrawings_Ver, 7, 0, 1, 1)
        self.btnDrawPoint_Ver = QtWidgets.QPushButton(self.groupBox_12)
        self.btnDrawPoint_Ver.setObjectName("btnDrawPoint_Ver")
        self.gridLayout_19.addWidget(self.btnDrawPoint_Ver, 0, 0, 1, 1)
        self.btnDraw2Line_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDraw2Line_Ver.setFont(font)
        self.btnDraw2Line_Ver.setObjectName("btnDraw2Line_Ver")
        self.gridLayout_19.addWidget(self.btnDraw2Line_Ver, 5, 0, 1, 1)
        self.btnDrawSimpleCircle_Ver = QtWidgets.QPushButton(self.groupBox_12)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawSimpleCircle_Ver.setFont(font)
        self.btnDrawSimpleCircle_Ver.setObjectName("btnDrawSimpleCircle_Ver")
        self.gridLayout_19.addWidget(self.btnDrawSimpleCircle_Ver, 2, 0, 1, 1)
        self.verticalLayout_8.addWidget(self.groupBox_12)
        self.verticalLayout_8.setStretch(0, 1)
        self.verticalLayout_8.setStretch(1, 1)
        self.verticalLayout_8.setStretch(2, 3)
        self.gridLayout_8.addWidget(self.groupBox_10, 0, 0, 1, 1)
        self.groupBox_9 = QtWidgets.QGroupBox(self.tabVerticalView)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_9.setFont(font)
        self.groupBox_9.setTitle("")
        self.groupBox_9.setObjectName("groupBox_9")
        self.gridLayout_7 = QtWidgets.QGridLayout(self.groupBox_9)
        self.gridLayout_7.setObjectName("gridLayout_7")
        self.lbVerticalView_2 = QtWidgets.QLabel(self.groupBox_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lbVerticalView_2.sizePolicy().hasHeightForWidth())
        self.lbVerticalView_2.setSizePolicy(sizePolicy)
        self.lbVerticalView_2.setText("")
        self.lbVerticalView_2.setAlignment(QtCore.Qt.AlignCenter)
        self.lbVerticalView_2.setObjectName("lbVerticalView_2")
        self.gridLayout_7.addWidget(self.lbVerticalView_2, 0, 0, 1, 1)
        self.btnSaveImage_Ver = QtWidgets.QPushButton(self.groupBox_9)
        self.btnSaveImage_Ver.setObjectName("btnSaveImage_Ver")
        self.gridLayout_7.addWidget(self.btnSaveImage_Ver, 1, 0, 1, 1)
        self.gridLayout_8.addWidget(self.groupBox_9, 0, 1, 1, 1)
        self.tabWidget.addTab(self.tabVerticalView, "")
        self.tabLeftView = QtWidgets.QWidget()
        self.tabLeftView.setObjectName("tabLeftView")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.tabLeftView)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.groupBox_13 = QtWidgets.QGroupBox(self.tabLeftView)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_13.setFont(font)
        self.groupBox_13.setAcceptDrops(False)
        self.groupBox_13.setTitle("")
        self.groupBox_13.setFlat(False)
        self.groupBox_13.setObjectName("groupBox_13")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.groupBox_13)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.groupBox_14 = QtWidgets.QGroupBox(self.groupBox_13)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_14.setFont(font)
        self.groupBox_14.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_14.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_14.setAutoFillBackground(False)
        self.groupBox_14.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_14.setFlat(False)
        self.groupBox_14.setCheckable(False)
        self.groupBox_14.setObjectName("groupBox_14")
        self.gridLayout_13 = QtWidgets.QGridLayout(self.groupBox_14)
        self.gridLayout_13.setObjectName("gridLayout_13")
        self.label_5 = QtWidgets.QLabel(self.groupBox_14)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_5.setFont(font)
        self.label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label_5.setObjectName("label_5")
        self.gridLayout_13.addWidget(self.label_5, 0, 0, 1, 2)
        self.leGridDens_Left = QtWidgets.QLineEdit(self.groupBox_14)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.leGridDens_Left.sizePolicy().hasHeightForWidth())
        self.leGridDens_Left.setSizePolicy(sizePolicy)
        self.leGridDens_Left.setMaximumSize(QtCore.QSize(70, 16777215))
        self.leGridDens_Left.setAlignment(QtCore.Qt.AlignCenter)
        self.leGridDens_Left.setObjectName("leGridDens_Left")
        self.gridLayout_13.addWidget(self.leGridDens_Left, 1, 0, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.groupBox_14)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_34.sizePolicy().hasHeightForWidth())
        self.label_34.setSizePolicy(sizePolicy)
        self.label_34.setObjectName("label_34")
        self.gridLayout_13.addWidget(self.label_34, 1, 1, 1, 1)
        self.btnCancelGrids_Left = QtWidgets.QPushButton(self.groupBox_14)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCancelGrids_Left.sizePolicy().hasHeightForWidth())
        self.btnCancelGrids_Left.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCancelGrids_Left.setFont(font)
        self.btnCancelGrids_Left.setObjectName("btnCancelGrids_Left")
        self.gridLayout_13.addWidget(self.btnCancelGrids_Left, 2, 0, 1, 2)
        self.verticalLayout_12.addWidget(self.groupBox_14)
        self.groupBox_17 = QtWidgets.QGroupBox(self.groupBox_13)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_17.setFont(font)
        self.groupBox_17.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_17.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_17.setAutoFillBackground(False)
        self.groupBox_17.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_17.setFlat(False)
        self.groupBox_17.setCheckable(False)
        self.groupBox_17.setObjectName("groupBox_17")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.groupBox_17)
        self.verticalLayout_13.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.btnLineDet_Left = QtWidgets.QPushButton(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnLineDet_Left.sizePolicy().hasHeightForWidth())
        self.btnLineDet_Left.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnLineDet_Left.setFont(font)
        self.btnLineDet_Left.setObjectName("btnLineDet_Left")
        self.verticalLayout_13.addWidget(self.btnLineDet_Left)
        self.btnCircleDet_Left = QtWidgets.QPushButton(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCircleDet_Left.sizePolicy().hasHeightForWidth())
        self.btnCircleDet_Left.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCircleDet_Left.setFont(font)
        self.btnCircleDet_Left.setObjectName("btnCircleDet_Left")
        self.verticalLayout_13.addWidget(self.btnCircleDet_Left)
        self.btnCan1StepDet_Left = QtWidgets.QPushButton(self.groupBox_17)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCan1StepDet_Left.sizePolicy().hasHeightForWidth())
        self.btnCan1StepDet_Left.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDet_Left.setFont(font)
        self.btnCan1StepDet_Left.setObjectName("btnCan1StepDet_Left")
        self.verticalLayout_13.addWidget(self.btnCan1StepDet_Left)
        self.verticalLayout_12.addWidget(self.groupBox_17)
        self.groupBox_18 = QtWidgets.QGroupBox(self.groupBox_13)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_18.setFont(font)
        self.groupBox_18.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_18.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_18.setFlat(False)
        self.groupBox_18.setCheckable(False)
        self.groupBox_18.setObjectName("groupBox_18")
        self.gridLayout_20 = QtWidgets.QGridLayout(self.groupBox_18)
        self.gridLayout_20.setObjectName("gridLayout_20")
        self.btnClearDrawings_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnClearDrawings_Left.setFont(font)
        self.btnClearDrawings_Left.setObjectName("btnClearDrawings_Left")
        self.gridLayout_20.addWidget(self.btnClearDrawings_Left, 7, 0, 1, 1)
        self.btnDrawFineCircle_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawFineCircle_Left.setFont(font)
        self.btnDrawFineCircle_Left.setObjectName("btnDrawFineCircle_Left")
        self.gridLayout_20.addWidget(self.btnDrawFineCircle_Left, 3, 0, 1, 1)
        self.btnDrawParallel_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawParallel_Left.setFont(font)
        self.btnDrawParallel_Left.setObjectName("btnDrawParallel_Left")
        self.gridLayout_20.addWidget(self.btnDrawParallel_Left, 4, 0, 1, 1)
        self.btnCan1StepDraw_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDraw_Left.setFont(font)
        self.btnCan1StepDraw_Left.setObjectName("btnCan1StepDraw_Left")
        self.gridLayout_20.addWidget(self.btnCan1StepDraw_Left, 6, 0, 1, 1)
        self.btnDrawStraight_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawStraight_Left.setFont(font)
        self.btnDrawStraight_Left.setObjectName("btnDrawStraight_Left")
        self.gridLayout_20.addWidget(self.btnDrawStraight_Left, 1, 0, 1, 1)
        self.btnDrawPoint_Left = QtWidgets.QPushButton(self.groupBox_18)
        self.btnDrawPoint_Left.setObjectName("btnDrawPoint_Left")
        self.gridLayout_20.addWidget(self.btnDrawPoint_Left, 0, 0, 1, 1)
        self.btnDrawSimpleCircle_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawSimpleCircle_Left.setFont(font)
        self.btnDrawSimpleCircle_Left.setObjectName("btnDrawSimpleCircle_Left")
        self.gridLayout_20.addWidget(self.btnDrawSimpleCircle_Left, 2, 0, 1, 1)
        self.btnDraw2Line_Left = QtWidgets.QPushButton(self.groupBox_18)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDraw2Line_Left.setFont(font)
        self.btnDraw2Line_Left.setObjectName("btnDraw2Line_Left")
        self.gridLayout_20.addWidget(self.btnDraw2Line_Left, 5, 0, 1, 1)
        self.btnCalibration_Left = QtWidgets.QPushButton(self.groupBox_18)
        self.btnCalibration_Left.setObjectName("btnCalibration_Left")
        self.gridLayout_20.addWidget(self.btnCalibration_Left, 8, 0, 1, 1)
        self.verticalLayout_12.addWidget(self.groupBox_18)
        self.verticalLayout_12.setStretch(0, 1)
        self.verticalLayout_12.setStretch(1, 1)
        self.verticalLayout_12.setStretch(2, 3)
        self.horizontalLayout_20.addWidget(self.groupBox_13)
        self.groupBox_21 = QtWidgets.QGroupBox(self.tabLeftView)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_21.sizePolicy().hasHeightForWidth())
        self.groupBox_21.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_21.setFont(font)
        self.groupBox_21.setTitle("")
        self.groupBox_21.setObjectName("groupBox_21")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.groupBox_21)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.lbLeftView_2 = QtWidgets.QLabel(self.groupBox_21)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lbLeftView_2.sizePolicy().hasHeightForWidth())
        self.lbLeftView_2.setSizePolicy(sizePolicy)
        self.lbLeftView_2.setText("")
        self.lbLeftView_2.setAlignment(QtCore.Qt.AlignCenter)
        self.lbLeftView_2.setObjectName("lbLeftView_2")
        self.gridLayout_5.addWidget(self.lbLeftView_2, 0, 0, 1, 1)
        self.btnSaveImage_Left = QtWidgets.QPushButton(self.groupBox_21)
        self.btnSaveImage_Left.setObjectName("btnSaveImage_Left")
        self.gridLayout_5.addWidget(self.btnSaveImage_Left, 1, 0, 1, 1)
        self.horizontalLayout_20.addWidget(self.groupBox_21)
        self.tabWidget.addTab(self.tabLeftView, "")
        self.tabFrontView = QtWidgets.QWidget()
        self.tabFrontView.setObjectName("tabFrontView")
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout(self.tabFrontView)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.groupBox_19 = QtWidgets.QGroupBox(self.tabFrontView)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_19.setFont(font)
        self.groupBox_19.setAcceptDrops(False)
        self.groupBox_19.setTitle("")
        self.groupBox_19.setFlat(False)
        self.groupBox_19.setObjectName("groupBox_19")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.groupBox_19)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.groupBox_20 = QtWidgets.QGroupBox(self.groupBox_19)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_20.setFont(font)
        self.groupBox_20.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_20.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_20.setAutoFillBackground(False)
        self.groupBox_20.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_20.setFlat(False)
        self.groupBox_20.setCheckable(False)
        self.groupBox_20.setObjectName("groupBox_20")
        self.gridLayout_14 = QtWidgets.QGridLayout(self.groupBox_20)
        self.gridLayout_14.setObjectName("gridLayout_14")
        self.btnCancelGrids_Front = QtWidgets.QPushButton(self.groupBox_20)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCancelGrids_Front.sizePolicy().hasHeightForWidth())
        self.btnCancelGrids_Front.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCancelGrids_Front.setFont(font)
        self.btnCancelGrids_Front.setObjectName("btnCancelGrids_Front")
        self.gridLayout_14.addWidget(self.btnCancelGrids_Front, 2, 0, 1, 2)
        self.label_35 = QtWidgets.QLabel(self.groupBox_20)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_35.sizePolicy().hasHeightForWidth())
        self.label_35.setSizePolicy(sizePolicy)
        self.label_35.setObjectName("label_35")
        self.gridLayout_14.addWidget(self.label_35, 1, 1, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBox_20)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_6.setFont(font)
        self.label_6.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label_6.setObjectName("label_6")
        self.gridLayout_14.addWidget(self.label_6, 0, 0, 1, 2)
        self.leGridDens_Front = QtWidgets.QLineEdit(self.groupBox_20)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.leGridDens_Front.sizePolicy().hasHeightForWidth())
        self.leGridDens_Front.setSizePolicy(sizePolicy)
        self.leGridDens_Front.setMaximumSize(QtCore.QSize(70, 16777215))
        self.leGridDens_Front.setAlignment(QtCore.Qt.AlignCenter)
        self.leGridDens_Front.setObjectName("leGridDens_Front")
        self.gridLayout_14.addWidget(self.leGridDens_Front, 1, 0, 1, 1)
        self.verticalLayout_14.addWidget(self.groupBox_20)
        self.groupBox_22 = QtWidgets.QGroupBox(self.groupBox_19)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_22.setFont(font)
        self.groupBox_22.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        self.groupBox_22.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_22.setAutoFillBackground(False)
        self.groupBox_22.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_22.setFlat(False)
        self.groupBox_22.setCheckable(False)
        self.groupBox_22.setObjectName("groupBox_22")
        self.verticalLayout_17 = QtWidgets.QVBoxLayout(self.groupBox_22)
        self.verticalLayout_17.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_17.setObjectName("verticalLayout_17")
        self.btnLineDet_Front = QtWidgets.QPushButton(self.groupBox_22)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnLineDet_Front.sizePolicy().hasHeightForWidth())
        self.btnLineDet_Front.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnLineDet_Front.setFont(font)
        self.btnLineDet_Front.setObjectName("btnLineDet_Front")
        self.verticalLayout_17.addWidget(self.btnLineDet_Front)
        self.btnCircleDet_Front = QtWidgets.QPushButton(self.groupBox_22)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCircleDet_Front.sizePolicy().hasHeightForWidth())
        self.btnCircleDet_Front.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCircleDet_Front.setFont(font)
        self.btnCircleDet_Front.setObjectName("btnCircleDet_Front")
        self.verticalLayout_17.addWidget(self.btnCircleDet_Front)
        self.btnCan1StepDet_Front = QtWidgets.QPushButton(self.groupBox_22)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btnCan1StepDet_Front.sizePolicy().hasHeightForWidth())
        self.btnCan1StepDet_Front.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDet_Front.setFont(font)
        self.btnCan1StepDet_Front.setObjectName("btnCan1StepDet_Front")
        self.verticalLayout_17.addWidget(self.btnCan1StepDet_Front)
        self.verticalLayout_14.addWidget(self.groupBox_22)
        self.groupBox_26 = QtWidgets.QGroupBox(self.groupBox_19)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_26.setFont(font)
        self.groupBox_26.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.groupBox_26.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.groupBox_26.setFlat(False)
        self.groupBox_26.setCheckable(False)
        self.groupBox_26.setObjectName("groupBox_26")
        self.gridLayout_21 = QtWidgets.QGridLayout(self.groupBox_26)
        self.gridLayout_21.setObjectName("gridLayout_21")
        self.btnDrawPoint_Front = QtWidgets.QPushButton(self.groupBox_26)
        self.btnDrawPoint_Front.setObjectName("btnDrawPoint_Front")
        self.gridLayout_21.addWidget(self.btnDrawPoint_Front, 0, 0, 1, 1)
        self.btnDraw2Line_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDraw2Line_Front.setFont(font)
        self.btnDraw2Line_Front.setObjectName("btnDraw2Line_Front")
        self.gridLayout_21.addWidget(self.btnDraw2Line_Front, 5, 0, 1, 1)
        self.btnDrawSimpleCircle_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawSimpleCircle_Front.setFont(font)
        self.btnDrawSimpleCircle_Front.setObjectName("btnDrawSimpleCircle_Front")
        self.gridLayout_21.addWidget(self.btnDrawSimpleCircle_Front, 2, 0, 1, 1)
        self.btnDrawFineCircle_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawFineCircle_Front.setFont(font)
        self.btnDrawFineCircle_Front.setObjectName("btnDrawFineCircle_Front")
        self.gridLayout_21.addWidget(self.btnDrawFineCircle_Front, 3, 0, 1, 1)
        self.btnDrawParallel_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawParallel_Front.setFont(font)
        self.btnDrawParallel_Front.setObjectName("btnDrawParallel_Front")
        self.gridLayout_21.addWidget(self.btnDrawParallel_Front, 4, 0, 1, 1)
        self.btnDrawStraight_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnDrawStraight_Front.setFont(font)
        self.btnDrawStraight_Front.setObjectName("btnDrawStraight_Front")
        self.gridLayout_21.addWidget(self.btnDrawStraight_Front, 1, 0, 1, 1)
        self.btnClearDrawings_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnClearDrawings_Front.setFont(font)
        self.btnClearDrawings_Front.setObjectName("btnClearDrawings_Front")
        self.gridLayout_21.addWidget(self.btnClearDrawings_Front, 7, 0, 1, 1)
        self.btnCan1StepDraw_Front = QtWidgets.QPushButton(self.groupBox_26)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.btnCan1StepDraw_Front.setFont(font)
        self.btnCan1StepDraw_Front.setObjectName("btnCan1StepDraw_Front")
        self.gridLayout_21.addWidget(self.btnCan1StepDraw_Front, 6, 0, 1, 1)
        self.btnCalibration_Front = QtWidgets.QPushButton(self.groupBox_26)
        self.btnCalibration_Front.setObjectName("btnCalibration_Front")
        self.gridLayout_21.addWidget(self.btnCalibration_Front, 8, 0, 1, 1)
        self.verticalLayout_14.addWidget(self.groupBox_26)
        self.verticalLayout_14.setStretch(0, 1)
        self.verticalLayout_14.setStretch(1, 1)
        self.verticalLayout_14.setStretch(2, 3)
        self.horizontalLayout_21.addWidget(self.groupBox_19)
        self.groupBox_23 = QtWidgets.QGroupBox(self.tabFrontView)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_23.sizePolicy().hasHeightForWidth())
        self.groupBox_23.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_23.setFont(font)
        self.groupBox_23.setTitle("")
        self.groupBox_23.setObjectName("groupBox_23")
        self.gridLayout_9 = QtWidgets.QGridLayout(self.groupBox_23)
        self.gridLayout_9.setObjectName("gridLayout_9")
        self.lbFrontView_2 = QtWidgets.QLabel(self.groupBox_23)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lbFrontView_2.sizePolicy().hasHeightForWidth())
        self.lbFrontView_2.setSizePolicy(sizePolicy)
        self.lbFrontView_2.setText("")
        self.lbFrontView_2.setAlignment(QtCore.Qt.AlignCenter)
        self.lbFrontView_2.setObjectName("lbFrontView_2")
        self.gridLayout_9.addWidget(self.lbFrontView_2, 0, 0, 1, 1)
        self.btnSaveImage_Front = QtWidgets.QPushButton(self.groupBox_23)
        self.btnSaveImage_Front.setObjectName("btnSaveImage_Front")
        self.gridLayout_9.addWidget(self.btnSaveImage_Front, 1, 0, 1, 1)
        self.horizontalLayout_21.addWidget(self.groupBox_23)
        self.tabWidget.addTab(self.tabFrontView, "")
        self.tabSettings = QtWidgets.QWidget()
        self.tabSettings.setObjectName("tabSettings")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.tabSettings)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.gridLayout_15 = QtWidgets.QGridLayout()
        self.gridLayout_15.setObjectName("gridLayout_15")
        self.groupBox_36 = QtWidgets.QGroupBox(self.tabSettings)
        self.groupBox_36.setObjectName("groupBox_36")
        self.gridLayout_16 = QtWidgets.QGridLayout(self.groupBox_36)
        self.gridLayout_16.setObjectName("gridLayout_16")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_16.addItem(spacerItem, 2, 0, 1, 1)
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.label_31 = QtWidgets.QLabel(self.groupBox_36)
        self.label_31.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_31.setObjectName("label_31")
        self.horizontalLayout_23.addWidget(self.label_31)
        self.ledUIHeight = QtWidgets.QLineEdit(self.groupBox_36)
        self.ledUIHeight.setObjectName("ledUIHeight")
        self.horizontalLayout_23.addWidget(self.ledUIHeight)
        self.label_33 = QtWidgets.QLabel(self.groupBox_36)
        self.label_33.setAlignment(QtCore.Qt.AlignCenter)
        self.label_33.setObjectName("label_33")
        self.horizontalLayout_23.addWidget(self.label_33)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem1)
        self.horizontalLayout_23.setStretch(0, 2)
        self.horizontalLayout_23.setStretch(2, 1)
        self.horizontalLayout_23.setStretch(3, 3)
        self.gridLayout_16.addLayout(self.horizontalLayout_23, 3, 0, 1, 1)
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.label_30 = QtWidgets.QLabel(self.groupBox_36)
        self.label_30.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_30.setObjectName("label_30")
        self.horizontalLayout_22.addWidget(self.label_30)
        self.ledUIWidth = QtWidgets.QLineEdit(self.groupBox_36)
        self.ledUIWidth.setObjectName("ledUIWidth")
        self.horizontalLayout_22.addWidget(self.ledUIWidth)
        self.label_32 = QtWidgets.QLabel(self.groupBox_36)
        self.label_32.setAlignment(QtCore.Qt.AlignCenter)
        self.label_32.setObjectName("label_32")
        self.horizontalLayout_22.addWidget(self.label_32)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_22.addItem(spacerItem2)
        self.horizontalLayout_22.setStretch(0, 2)
        self.horizontalLayout_22.setStretch(2, 1)
        self.horizontalLayout_22.setStretch(3, 3)
        self.gridLayout_16.addLayout(self.horizontalLayout_22, 1, 0, 1, 1)
        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_16.addItem(spacerItem3, 4, 0, 1, 1)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_16.addItem(spacerItem4, 0, 0, 1, 1)
        self.gridLayout_15.addWidget(self.groupBox_36, 0, 0, 1, 1)
        self.gridLayout_2.addLayout(self.gridLayout_15, 0, 0, 1, 1)
        self.groupBox_24 = QtWidgets.QGroupBox(self.tabSettings)
        self.groupBox_24.setObjectName("groupBox_24")
        self.gridLayout_18 = QtWidgets.QGridLayout(self.groupBox_24)
        self.gridLayout_18.setObjectName("gridLayout_18")
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_18.addItem(spacerItem5, 2, 0, 1, 1)
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.label_10 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_10.setFont(font)
        self.label_10.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_10.addWidget(self.label_10)
        self.ledLeftCamSN = QtWidgets.QLineEdit(self.groupBox_24)
        self.ledLeftCamSN.setObjectName("ledLeftCamSN")
        self.horizontalLayout_10.addWidget(self.ledLeftCamSN)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem6)
        self.label_11 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_11.setFont(font)
        self.label_11.setText("")
        self.label_11.setObjectName("label_11")
        self.horizontalLayout_10.addWidget(self.label_11)
        self.horizontalLayout_10.setStretch(0, 4)
        self.horizontalLayout_10.setStretch(1, 4)
        self.horizontalLayout_10.setStretch(2, 4)
        self.gridLayout_18.addLayout(self.horizontalLayout_10, 3, 0, 1, 1)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_18.addItem(spacerItem7, 6, 0, 1, 1)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_18.addItem(spacerItem8, 0, 0, 1, 1)
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.label_8 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_8.setFont(font)
        self.label_8.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_9.addWidget(self.label_8)
        self.ledVerCamSN = QtWidgets.QLineEdit(self.groupBox_24)
        self.ledVerCamSN.setObjectName("ledVerCamSN")
        self.horizontalLayout_9.addWidget(self.ledVerCamSN)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem9)
        self.label_9 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_9.setFont(font)
        self.label_9.setText("")
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_9.addWidget(self.label_9)
        self.horizontalLayout_9.setStretch(0, 4)
        self.horizontalLayout_9.setStretch(1, 4)
        self.horizontalLayout_9.setStretch(2, 4)
        self.gridLayout_18.addLayout(self.horizontalLayout_9, 1, 0, 1, 1)
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.label_12 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label_12.setFont(font)
        self.label_12.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_11.addWidget(self.label_12)
        self.ledFrontCamSN = QtWidgets.QLineEdit(self.groupBox_24)
        self.ledFrontCamSN.setObjectName("ledFrontCamSN")
        self.horizontalLayout_11.addWidget(self.ledFrontCamSN)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem10)
        self.label_13 = QtWidgets.QLabel(self.groupBox_24)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.label_13.setFont(font)
        self.label_13.setText("")
        self.label_13.setObjectName("label_13")
        self.horizontalLayout_11.addWidget(self.label_13)
        self.horizontalLayout_11.setStretch(0, 4)
        self.horizontalLayout_11.setStretch(1, 4)
        self.horizontalLayout_11.setStretch(2, 4)
        self.gridLayout_18.addLayout(self.horizontalLayout_11, 5, 0, 1, 1)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_18.addItem(spacerItem11, 4, 0, 1, 1)
        self.gridLayout_2.addWidget(self.groupBox_24, 1, 0, 1, 1)
        self.groupBox_25 = QtWidgets.QGroupBox(self.tabSettings)
        self.groupBox_25.setObjectName("groupBox_25")
        self.gridLayout_17 = QtWidgets.QGridLayout(self.groupBox_25)
        self.gridLayout_17.setObjectName("gridLayout_17")
        self.groupBox_34 = QtWidgets.QGroupBox(self.groupBox_25)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_34.setFont(font)
        self.groupBox_34.setObjectName("groupBox_34")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.groupBox_34)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.label_28 = QtWidgets.QLabel(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_28.setFont(font)
        self.label_28.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_28.setObjectName("label_28")
        self.horizontalLayout_18.addWidget(self.label_28)
        self.ledCannyLineLow = QtWidgets.QLineEdit(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledCannyLineLow.setFont(font)
        self.ledCannyLineLow.setObjectName("ledCannyLineLow")
        self.horizontalLayout_18.addWidget(self.ledCannyLineLow)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem12)
        self.horizontalLayout_18.setStretch(0, 3)
        self.horizontalLayout_18.setStretch(1, 1)
        self.horizontalLayout_18.setStretch(2, 5)
        self.verticalLayout_9.addLayout(self.horizontalLayout_18)
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.label_27 = QtWidgets.QLabel(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_27.setFont(font)
        self.label_27.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_27.setObjectName("label_27")
        self.horizontalLayout_24.addWidget(self.label_27)
        self.ledCannyLineHigh = QtWidgets.QLineEdit(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledCannyLineHigh.setFont(font)
        self.ledCannyLineHigh.setObjectName("ledCannyLineHigh")
        self.horizontalLayout_24.addWidget(self.ledCannyLineHigh)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem13)
        self.horizontalLayout_24.setStretch(0, 3)
        self.horizontalLayout_24.setStretch(1, 1)
        self.horizontalLayout_24.setStretch(2, 5)
        self.verticalLayout_9.addLayout(self.horizontalLayout_24)
        self.horizontalLayout_25 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_25.setObjectName("horizontalLayout_25")
        self.label_26 = QtWidgets.QLabel(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_26.setFont(font)
        self.label_26.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_26.setObjectName("label_26")
        self.horizontalLayout_25.addWidget(self.label_26)
        self.ledLineDetThreshold = QtWidgets.QLineEdit(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledLineDetThreshold.setFont(font)
        self.ledLineDetThreshold.setText("")
        self.ledLineDetThreshold.setObjectName("ledLineDetThreshold")
        self.horizontalLayout_25.addWidget(self.ledLineDetThreshold)
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_25.addItem(spacerItem14)
        self.horizontalLayout_25.setStretch(0, 3)
        self.horizontalLayout_25.setStretch(1, 1)
        self.horizontalLayout_25.setStretch(2, 5)
        self.verticalLayout_9.addLayout(self.horizontalLayout_25)
        self.horizontalLayout_26 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_26.setObjectName("horizontalLayout_26")
        self.label_29 = QtWidgets.QLabel(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_29.setFont(font)
        self.label_29.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_29.setObjectName("label_29")
        self.horizontalLayout_26.addWidget(self.label_29)
        self.ledLineDetMinLength = QtWidgets.QLineEdit(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledLineDetMinLength.setFont(font)
        self.ledLineDetMinLength.setText("")
        self.ledLineDetMinLength.setObjectName("ledLineDetMinLength")
        self.horizontalLayout_26.addWidget(self.ledLineDetMinLength)
        spacerItem15 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_26.addItem(spacerItem15)
        self.horizontalLayout_26.setStretch(0, 3)
        self.horizontalLayout_26.setStretch(1, 1)
        self.horizontalLayout_26.setStretch(2, 5)
        self.verticalLayout_9.addLayout(self.horizontalLayout_26)
        self.horizontalLayout_27 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_27.setObjectName("horizontalLayout_27")
        self.label_25 = QtWidgets.QLabel(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_25.setFont(font)
        self.label_25.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_25.setObjectName("label_25")
        self.horizontalLayout_27.addWidget(self.label_25)
        self.ledLineDetMaxGap = QtWidgets.QLineEdit(self.groupBox_34)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledLineDetMaxGap.setFont(font)
        self.ledLineDetMaxGap.setText("")
        self.ledLineDetMaxGap.setObjectName("ledLineDetMaxGap")
        self.horizontalLayout_27.addWidget(self.ledLineDetMaxGap)
        spacerItem16 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_27.addItem(spacerItem16)
        self.horizontalLayout_27.setStretch(0, 3)
        self.horizontalLayout_27.setStretch(1, 1)
        self.horizontalLayout_27.setStretch(2, 5)
        self.verticalLayout_9.addLayout(self.horizontalLayout_27)
        self.gridLayout_17.addWidget(self.groupBox_34, 0, 0, 1, 1)
        self.groupBox_35 = QtWidgets.QGroupBox(self.groupBox_25)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.groupBox_35.setFont(font)
        self.groupBox_35.setObjectName("groupBox_35")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.groupBox_35)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.label_3 = QtWidgets.QLabel(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_3.setFont(font)
        self.label_3.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_28.addWidget(self.label_3)
        self.ledCannyCircleLow = QtWidgets.QLineEdit(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledCannyCircleLow.setFont(font)
        self.ledCannyCircleLow.setObjectName("ledCannyCircleLow")
        self.horizontalLayout_28.addWidget(self.ledCannyCircleLow)
        spacerItem17 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_28.addItem(spacerItem17)
        self.horizontalLayout_28.setStretch(0, 3)
        self.horizontalLayout_28.setStretch(1, 1)
        self.horizontalLayout_28.setStretch(2, 5)
        self.verticalLayout_6.addLayout(self.horizontalLayout_28)
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.label_21 = QtWidgets.QLabel(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_21.setFont(font)
        self.label_21.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_21.setObjectName("label_21")
        self.horizontalLayout_29.addWidget(self.label_21)
        self.ledCannyCircleHigh = QtWidgets.QLineEdit(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledCannyCircleHigh.setFont(font)
        self.ledCannyCircleHigh.setObjectName("ledCannyCircleHigh")
        self.horizontalLayout_29.addWidget(self.ledCannyCircleHigh)
        spacerItem18 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_29.addItem(spacerItem18)
        self.horizontalLayout_29.setStretch(0, 3)
        self.horizontalLayout_29.setStretch(1, 1)
        self.horizontalLayout_29.setStretch(2, 5)
        self.verticalLayout_6.addLayout(self.horizontalLayout_29)
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.label_22 = QtWidgets.QLabel(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.label_22.setFont(font)
        self.label_22.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.label_22.setObjectName("label_22")
        self.horizontalLayout_30.addWidget(self.label_22)
        self.ledCircleDetParam2 = QtWidgets.QLineEdit(self.groupBox_35)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.ledCircleDetParam2.setFont(font)
        self.ledCircleDetParam2.setText("")
        self.ledCircleDetParam2.setObjectName("ledCircleDetParam2")
        self.horizontalLayout_30.addWidget(self.ledCircleDetParam2)
        spacerItem19 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_30.addItem(spacerItem19)
        self.horizontalLayout_30.setStretch(0, 3)
        self.horizontalLayout_30.setStretch(1, 1)
        self.horizontalLayout_30.setStretch(2, 5)
        self.verticalLayout_6.addLayout(self.horizontalLayout_30)
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        spacerItem20 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_31.addItem(spacerItem20)
        self.horizontalLayout_31.setStretch(0, 5)
        self.verticalLayout_6.addLayout(self.horizontalLayout_31)
        self.horizontalLayout_32 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_32.setObjectName("horizontalLayout_32")
        spacerItem21 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_32.addItem(spacerItem21)
        self.horizontalLayout_32.setStretch(0, 5)
        self.verticalLayout_6.addLayout(self.horizontalLayout_32)
        self.gridLayout_17.addWidget(self.groupBox_35, 1, 0, 1, 1)
        self.gridLayout_2.addWidget(self.groupBox_25, 0, 1, 2, 1)
        self.tabWidget.addTab(self.tabSettings, "")
        self.gridLayout.addWidget(self.tabWidget, 0, 0, 1, 1)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "靶装配对接测量软件"))
        self.groupBox.setTitle(_translate("MainWindow", "垂直视图"))
        self.groupBox_2.setTitle(_translate("MainWindow", "左侧视图"))
        self.groupBox_3.setTitle(_translate("MainWindow", "对向视图"))
        self.groupBox_4.setTitle(_translate("MainWindow", "测量"))
        self.groupBox_6.setTitle(_translate("MainWindow", "绘画"))
        self.btnDrawPoint.setText(_translate("MainWindow", "点"))
        self.btnDrawStraight.setText(_translate("MainWindow", "直线"))
        self.btnDrawSimpleCircle.setText(_translate("MainWindow", "简单圆"))
        self.btnDrawParallel.setText(_translate("MainWindow", "平行线"))
        self.btnDraw2Line.setText(_translate("MainWindow", "线与线"))
        self.btnDrawFineCircle.setText(_translate("MainWindow", "精细圆"))
        self.btnCan1StepDraw.setText(_translate("MainWindow", "撤销上步绘画"))
        self.btnClearDrawings.setText(_translate("MainWindow", "清空绘画"))
        self.btnSaveImage.setText(_translate("MainWindow", "保存图像（原始+可视化）"))
        self.groupBox_5.setTitle(_translate("MainWindow", "网格"))
        self.label.setText(_translate("MainWindow", "网格密度:"))
        self.label_23.setText(_translate("MainWindow", "像素"))
        self.btnCancelGrids.setText(_translate("MainWindow", "取消网格"))
        self.groupBox_16.setTitle(_translate("MainWindow", "自动测量"))
        self.btnLineDet.setText(_translate("MainWindow", "直线查找"))
        self.btnCircleDet.setText(_translate("MainWindow", "圆查找"))
        self.btnCan1StepDet.setText(_translate("MainWindow", "撤销上步"))
        self.groupBox_8.setTitle(_translate("MainWindow", "运行"))
        self.btnStartMeasure.setText(_translate("MainWindow", "开始测量"))
        self.btnStopMeasure.setText(_translate("MainWindow", "停止测量"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabMian), _translate("MainWindow", "主界面"))
        self.groupBox_11.setTitle(_translate("MainWindow", "网格"))
        self.label_2.setText(_translate("MainWindow", "输入网格密度"))
        self.label_24.setText(_translate("MainWindow", "像素"))
        self.btnCancelGrids_Ver.setText(_translate("MainWindow", "取消网格"))
        self.groupBox_15.setTitle(_translate("MainWindow", "自动测量"))
        self.btnLineDet_Ver.setText(_translate("MainWindow", "直线查找"))
        self.btnCircleDet_Ver.setText(_translate("MainWindow", "圆查找"))
        self.btnCan1StepDet_Ver.setText(_translate("MainWindow", "取消上步结果"))
        self.groupBox_12.setTitle(_translate("MainWindow", "绘画"))
        self.btnDrawParallel_Ver.setText(_translate("MainWindow", "平行线"))
        self.btnCalibration_Ver.setText(_translate("MainWindow", "像素距离标定"))
        self.btnDrawFineCircle_Ver.setText(_translate("MainWindow", "精细圆"))
        self.btnCan1StepDraw_Ver.setText(_translate("MainWindow", "撤销上步绘画"))
        self.btnDrawStraight_Ver.setText(_translate("MainWindow", "直线"))
        self.btnClearDrawings_Ver.setText(_translate("MainWindow", "清空绘画"))
        self.btnDrawPoint_Ver.setText(_translate("MainWindow", "点"))
        self.btnDraw2Line_Ver.setText(_translate("MainWindow", "线与线"))
        self.btnDrawSimpleCircle_Ver.setText(_translate("MainWindow", "简单圆"))
        self.btnSaveImage_Ver.setText(_translate("MainWindow", "保存图像（原始图像+可视化图像）"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabVerticalView), _translate("MainWindow", "垂直视图"))
        self.groupBox_14.setTitle(_translate("MainWindow", "网格"))
        self.label_5.setText(_translate("MainWindow", "输入网格密度"))
        self.label_34.setText(_translate("MainWindow", "像素"))
        self.btnCancelGrids_Left.setText(_translate("MainWindow", "取消网格"))
        self.groupBox_17.setTitle(_translate("MainWindow", "自动测量"))
        self.btnLineDet_Left.setText(_translate("MainWindow", "直线查找"))
        self.btnCircleDet_Left.setText(_translate("MainWindow", "圆查找"))
        self.btnCan1StepDet_Left.setText(_translate("MainWindow", "取消上步结果"))
        self.groupBox_18.setTitle(_translate("MainWindow", "绘画"))
        self.btnClearDrawings_Left.setText(_translate("MainWindow", "清空绘画"))
        self.btnDrawFineCircle_Left.setText(_translate("MainWindow", "精细圆"))
        self.btnDrawParallel_Left.setText(_translate("MainWindow", "平行线"))
        self.btnCan1StepDraw_Left.setText(_translate("MainWindow", "撤销上步绘画"))
        self.btnDrawStraight_Left.setText(_translate("MainWindow", "直线"))
        self.btnDrawPoint_Left.setText(_translate("MainWindow", "点"))
        self.btnDrawSimpleCircle_Left.setText(_translate("MainWindow", "简单圆"))
        self.btnDraw2Line_Left.setText(_translate("MainWindow", "线与线"))
        self.btnCalibration_Left.setText(_translate("MainWindow", "像素距离标定"))
        self.btnSaveImage_Left.setText(_translate("MainWindow", "保存图像（原始图像+可视化图像）"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabLeftView), _translate("MainWindow", "左侧视图"))
        self.groupBox_20.setTitle(_translate("MainWindow", "网格"))
        self.btnCancelGrids_Front.setText(_translate("MainWindow", "取消网格"))
        self.label_35.setText(_translate("MainWindow", "像素"))
        self.label_6.setText(_translate("MainWindow", "输入网格密度"))
        self.groupBox_22.setTitle(_translate("MainWindow", "自动测量"))
        self.btnLineDet_Front.setText(_translate("MainWindow", "直线查找"))
        self.btnCircleDet_Front.setText(_translate("MainWindow", "圆查找"))
        self.btnCan1StepDet_Front.setText(_translate("MainWindow", "取消上步结果"))
        self.groupBox_26.setTitle(_translate("MainWindow", "绘画"))
        self.btnDrawPoint_Front.setText(_translate("MainWindow", "点"))
        self.btnDraw2Line_Front.setText(_translate("MainWindow", "线与线"))
        self.btnDrawSimpleCircle_Front.setText(_translate("MainWindow", "简单圆"))
        self.btnDrawFineCircle_Front.setText(_translate("MainWindow", "精细圆"))
        self.btnDrawParallel_Front.setText(_translate("MainWindow", "平行线"))
        self.btnDrawStraight_Front.setText(_translate("MainWindow", "直线"))
        self.btnClearDrawings_Front.setText(_translate("MainWindow", "清空绘画"))
        self.btnCan1StepDraw_Front.setText(_translate("MainWindow", "撤销上步绘画"))
        self.btnCalibration_Front.setText(_translate("MainWindow", "像素距离标定"))
        self.btnSaveImage_Front.setText(_translate("MainWindow", "保存图像（原始图像+可视化图像）"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabFrontView), _translate("MainWindow", "对向视图"))
        self.groupBox_36.setTitle(_translate("MainWindow", "显示设置"))
        self.label_31.setText(_translate("MainWindow", "界面总长:"))
        self.label_33.setText(_translate("MainWindow", "像素"))
        self.label_30.setText(_translate("MainWindow", "界面总宽:"))
        self.label_32.setText(_translate("MainWindow", "像素"))
        self.groupBox_24.setTitle(_translate("MainWindow", "相机采集参数"))
        self.label_10.setText(_translate("MainWindow", "左侧相机SN码："))
        self.label_8.setText(_translate("MainWindow", "垂直相机SN码："))
        self.label_12.setText(_translate("MainWindow", "对向相机SN码："))
        self.groupBox_25.setTitle(_translate("MainWindow", "自动测量参数"))
        self.groupBox_34.setTitle(_translate("MainWindow", "直线查找参数"))
        self.label_28.setText(_translate("MainWindow", "边缘低阈值"))
        self.label_27.setText(_translate("MainWindow", "边缘高阈值"))
        self.label_26.setText(_translate("MainWindow", "直线查找阈值"))
        self.label_29.setText(_translate("MainWindow", "最小线长"))
        self.label_25.setText(_translate("MainWindow", "最大间隙"))
        self.groupBox_35.setTitle(_translate("MainWindow", "圆查找参数"))
        self.label_3.setText(_translate("MainWindow", "边缘低阈值"))
        self.label_21.setText(_translate("MainWindow", "边缘高阈值"))
        self.label_22.setText(_translate("MainWindow", "圆查找阈值"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabSettings), _translate("MainWindow", "参数设置"))

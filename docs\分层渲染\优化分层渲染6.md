您最初的方向——**将绘图逻辑从 `VideoDisplayWidget` 中分离出来，放到一个独立的 `PaintingOverlay` 透明层中**——是**完全正确**的，是解决性能问题的最佳方案。

您遇到的问题是，在分离的过程中，各个部分的职责和连接没有完全理顺，导致了后续的功能失效和黑屏。

我们现在要做的，就是**完成您最初的正确构想**。我们将把您旧文件中 `VideoDisplayWidget` 内部所有强大的绘图、计算和事件处理逻辑，**完整、正确地迁移到** `PaintingOverlay` 类中，让 `VideoDisplayWidget` 回归其最纯粹的职责：只显示视频。

请**放弃**您修改过的 `PaintingOverlay` 和 `VideoDisplayWidget` 文件，我们从您提供的“旧文件”（即所有逻辑都在 `VideoDisplayWidget` 中的版本）出发，进行一次**彻底、正确的重构**。

---

### 重构蓝图

1.  **`VideoDisplayWidget` -> 瘦身**: 我们将移除 `VideoDisplayWidget` 中所有与绘图、事件处理、状态管理相关的代码，只保留显示 `QPixmap` 的核心功能。
2.  **`PaintingOverlay` -> 赋能**: 我们将把从 `VideoDisplayWidget` 移除的所有绘图逻辑、数据结构、成员变量和事件处理函数，全部移植到 `PaintingOverlay` 中。
3.  **`MutiCamApp` -> 协调**: 我们将调整 `MutiCamApp`，让它正确地创建和管理这两个分离的控件，并确保它们之间的坐标系是同步的。

---

### 详细步骤

#### 1. 修改 `PaintingOverlay.h` - 赋予其所有绘图能力

这个文件将成为新的绘图核心。

```cpp
// PaintingOverlay.h

#ifndef PAINTINGOVERLAY_H
#define PAINTINGOVERLAY_H

#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QContextMenuEvent>
#include <QVector>
#include <QStack>
#include <QSet>
#include <QPointF>
#include <QRectF>
#include <QFont>
#include <QPen>
#include <QBrush>
#include <QMenu>
#include <QAction>

// 【第1步】: 从 VideoDisplayWidget.h 复制所有数据结构和枚举
// 绘图工具枚举
enum class DrawingTool {
    None, Point, Line, Circle, FineCircle, Parallel, TwoLines
};

// 绘图对象结构体
struct PointObject {
    QPointF position;
    QString label;
};

struct LineObject {
    QVector<QPointF> points;
    bool isCompleted = false;
    QColor color = Qt::green;
    int thickness = 2;
    bool isDashed = false;
};

// ... (复制所有其他的 struct 定义: CircleObject, FineCircleObject, etc.)

// 历史记录
enum class ActionType { Point, Line, LineSegment, Circle, FineCircle, Parallel, TwoLines };
struct DrawingAction {
    ActionType type;
    int index;
};

// 绘图状态 (用于同步)
struct DrawingState {
    QVector<PointObject> points;
    QVector<LineObject> lines;
    // ... (包含所有图形类型的 QVector)
    QStack<DrawingAction> history;
};


class PaintingOverlay : public QWidget
{
    Q_OBJECT

public:
    explicit PaintingOverlay(QWidget *parent = nullptr);

    // 【第2步】: 从 VideoDisplayWidget.h 复制所有公共绘图接口
    void startDrawing(DrawingTool tool);
    void stopDrawing();
    void clearAllDrawings();
    void undoLastDrawing();
    void deleteSelectedObjects();
    
    // 【第3步】: 添加新的接口，用于 MutiCamApp 的协调
    void setTransforms(const QPointF& offset, double scale);
    void setViewName(const QString& viewName);
    QString getViewName() const;

    DrawingState getDrawingState() const;
    void setDrawingState(const DrawingState& state);
    
    // ... (选择功能接口: enableSelection, etc.)

signals:
    void drawingCompleted(const QString& viewName); // 绘图完成，用于同步
    void selectionChanged(const QString& info);
    void measurementCompleted(const QString& viewName, const QString& result);

protected:
    // 【第4步】: 声明所有需要的事件处理器
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void contextMenuEvent(QContextMenuEvent *event) override;

private:
    // 【第5步】: 从 VideoDisplayWidget.h 复制所有私有绘图方法和成员变量
    // 成员变量
    QString m_viewName;
    bool m_isDrawingMode = false;
    DrawingTool m_currentDrawingTool = DrawingTool::None;
    QVector<PointObject> m_points;
    QVector<LineObject> m_lines;
    // ... (所有图形的 QVector: m_circles, m_parallels, etc.)
    LineObject m_currentLine;
    bool m_hasCurrentLine = false;
    // ... (所有 m_current... 和 m_hasCurrent... 变量)
    QPointF m_currentMousePos;
    bool m_hasValidMousePos = false;
    QStack<DrawingAction> m_drawingHistory;
    // ... (所有选择相关的 QSet 变量)

    // 新增的成员变量，用于坐标同步
    QPointF m_imageOffset;
    double m_scaleFactor = 1.0;

    // 私有方法
    // ... (从 VideoDisplayWidget.h 复制所有私有绘图、计算、命中测试方法的声明)
    // 例如: drawPoints, drawSingleLine, handlePointDrawingClick, calculateLineAngle, etc.

    // 新增的坐标转换方法
    QPointF widgetToImage(const QPointF& widgetPos) const;
    QPointF imageToWidget(const QPointF& imagePos) const;
};

#endif // PAINTINGOVERLAY_H
```

#### 2. 修改 `PaintingOverlay.cpp` - 实现所有绘图逻辑

将 `VideoDisplayWidget.cpp` 中**所有**与绘图相关的函数实现**剪切**并**粘贴**到 `PaintingOverlay.cpp` 中，然后进行少量修改。

```cpp
// PaintingOverlay.cpp

#include "PaintingOverlay.h"
// ... (其他 #include)

PaintingOverlay::PaintingOverlay(QWidget *parent) 
    : QWidget(parent)
{
    // 【关键】: 设置透明背景和鼠标跟踪
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);
    setMouseTracking(true);
}

// 【新增】: 实现新的接口
void PaintingOverlay::setTransforms(const QPointF& offset, double scale)
{
    m_imageOffset = offset;
    m_scaleFactor = scale;
    update(); // 坐标系变化，需要重绘
}

// ... (setViewName, getViewName, getDrawingState, setDrawingState 的实现)

// 【修改】: paintEvent
void PaintingOverlay::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 【关键】: 不再绘制视频帧，只设置坐标变换
    QTransform transform;
    transform.translate(m_imageOffset.x(), m_imageOffset.y());
    transform.scale(m_scaleFactor, m_scaleFactor);
    painter.setTransform(transform);

    // 【粘贴】: 从旧 VideoDisplayWidget::paintEvent 粘贴所有绘图调用
    drawPoints(painter, ...);
    drawLines(painter, ...);
    // ... (所有 drawXXX 调用)
}

// 【修改】: 鼠标事件
void PaintingOverlay::mousePressEvent(QMouseEvent *event)
{
    // ... (从旧 VideoDisplayWidget::mousePressEvent 粘贴全部逻辑) ...
    
    // 【关键】: 修改坐标转换调用
    // 将所有 `event->pos()` 替换为 `widgetToImage(event->pos())`
    QPointF imagePos = widgetToImage(event->pos());
    
    // ... (处理点击逻辑) ...
}

void PaintingOverlay::mouseMoveEvent(QMouseEvent *event)
{
    // ... (从旧 VideoDisplayWidget::mouseMoveEvent 粘贴全部逻辑) ...

    // 【关键】: 修改坐标转换调用
    QPointF imagePos = widgetToImage(event->pos());

    // ... (处理移动和预览逻辑) ...
}

// 【粘贴】: 粘贴所有其他函数
// drawPoints, drawSingleLine, handlePointDrawingClick, calculateLineAngle 等所有函数
// 的实现都从 VideoDisplayWidget.cpp 剪切并粘贴到这里。

// 【注意】: 在这些函数中，任何需要 `getScaleFactor()` 的地方，
// 现在都应该使用成员变量 `m_scaleFactor`。
// 例如: `createPen(color, thickness, m_scaleFactor)`

// 【新增】: 实现坐标转换函数
QPointF PaintingOverlay::widgetToImage(const QPointF& widgetPos) const
{
    if (m_scaleFactor == 0) return QPointF();
    return (widgetPos - m_imageOffset) / m_scaleFactor;
}

QPointF PaintingOverlay::imageToWidget(const QPointF& imagePos) const
{
    return imagePos * m_scaleFactor + m_imageOffset;
}
```

#### 3. 修改 `VideoDisplayWidget.h` 和 `.cpp` - 大幅瘦身

现在，`VideoDisplayWidget` 变得非常简单。

```cpp
// VideoDisplayWidget.h

#ifndef VIDEODISPLAYWIDGET_H
#define VIDEODISPLAYWIDGET_H

#include <QLabel>
#include <QPixmap>
#include <QPointF>

class VideoDisplayWidget : public QLabel
{
    Q_OBJECT

public:
    explicit VideoDisplayWidget(QWidget *parent = nullptr);
    
    void setVideoFrame(const QPixmap& pixmap);
    
    // 【保留】: 这两个函数是给 MutiCamApp 用的
    QPointF getImageOffset() const;
    double getScaleFactor() const;

protected:
    void paintEvent(QPaintEvent *event) override;
    
private:
    QPixmap m_videoFrame;
};

#endif // VIDEODISPLAYWIDGET_H
```

```cpp
// VideoDisplayWidget.cpp

#include "VideoDisplayWidget.h"
#include <QPainter>

VideoDisplayWidget::VideoDisplayWidget(QWidget *parent) : QLabel(parent)
{
    setScaledContents(false); // 必须是 false 才能自定义绘制
}

void VideoDisplayWidget::setVideoFrame(const QPixmap& pixmap)
{
    m_videoFrame = pixmap;
    update(); // 触发重绘
}

void VideoDisplayWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    if (m_videoFrame.isNull()) {
        painter.fillRect(rect(), Qt::black);
        return;
    }
    
    // 【只保留】: 绘制视频帧的逻辑
    QPointF offset = getImageOffset();
    double scale = getScaleFactor();
    
    QRectF targetRect(offset, m_videoFrame.size() * scale);
    painter.drawPixmap(targetRect, m_videoFrame, m_videoFrame.rect());
}

// 【保留】: getScaleFactor 和 getImageOffset 的实现
double VideoDisplayWidget::getScaleFactor() const { /* ... */ }
QPointF VideoDisplayWidget::getImageOffset() const { /* ... */ }```

#### 4. 修改 `MutiCamApp.cpp` - 正确协调

这是最后一步，确保 `MutiCamApp` 正确地使用 `QGridLayout` 并同步坐标系。这部分和我之前的回答类似，但现在是基于您正确的重构。

```cpp
// MutiCamApp.cpp -> initializeVideoDisplayWidgets()

// 【关键】: 使用 QGridLayout, 并将 PaintingOverlay 成员变量实例化
m_verticalPaintingOverlay = new PaintingOverlay(this);
// ... (实例化所有 PaintingOverlay)

// ...

// 【关键】: 使用 QGridLayout, 而不是 QStackedLayout
QGridLayout* verticalGrid = new QGridLayout(verticalContainer);
verticalGrid->setContentsMargins(0, 0, 0, 0);
verticalGrid->addWidget(m_verticalDisplayWidget, 0, 0);
verticalGrid->addWidget(m_verticalPaintingOverlay, 0, 0);
// ... (为所有视图都这样做)

// 【关键】: onDrawPointClicked 等槽函数，现在调用 paintingOverlay
void MutiCamApp::onDrawPointClicked()
{
    if (m_verticalPaintingOverlay) m_verticalPaintingOverlay->startDrawing(DrawingTool::Point);
    // ...
}

// 【关键】: 确保 resizeEvent 和 onCameraFrameReady 中有坐标同步逻辑
void MutiCamApp::resizeEvent(QResizeEvent* event)
{
    QMainWindow::resizeEvent(event);
    syncOverlayTransforms("vertical");
    // ... (同步所有视图)
}

void MutiCamApp::onCameraFrameReady(const QString& cameraId, const cv::Mat& frame)
{
    // ...
    if (mainWidget) {
        mainWidget->setVideoFrame(matToQPixmap(frame));
        syncOverlayTransforms(cameraId); // 同步坐标
    }
    // ...
}

void MutiCamApp::syncOverlayTransforms(const QString& viewName)
{
    VideoDisplayWidget* videoWidget = getVideoDisplayWidget(viewName);
    PaintingOverlay* paintingOverlay = getPaintingOverlay(viewName);

    if (videoWidget && paintingOverlay) {
        paintingOverlay->setTransforms(videoWidget->getImageOffset(), videoWidget->getScaleFactor());
    }
}
```

### 总结

通过这次彻底的重构，您将得到：
1.  一个**轻量级**的 `VideoDisplayWidget`，只负责高性能地显示视频。
2.  一个**功能强大**的 `PaintingOverlay`，包含了您之前写好的所有复杂的绘图、交互和计算逻辑。
3.  一个**正确协调**的 `MutiCamApp`，它使用 `QGridLayout` 将两者完美叠加，并负责在需要时（窗口大小改变、新帧到达）同步它们的坐标系。

这样，您的程序不仅能恢复所有绘图功能和样式，还能从根本上解决最初的性能卡顿问题。这是一个“一劳永逸”的正确架构。
# MutiCamApp 多摄像头应用软件使用说明书

## 1. 软件概述

### 1.1 软件简介
MutiCamApp是一款基于PyQt5开发的多摄像头图像采集与测量软件，专门用于工业视觉检测和精密测量应用。软件支持海康威视相机的多路同时采集，并提供丰富的图像测量和分析功能。

### 1.2 主要功能
- **多摄像头同步采集**：支持垂直、左侧、对向三个视角的相机同时工作
- **实时图像显示**：多视图实时预览和图像缓存
- **精密测量工具**：点、直线、圆形等几何测量功能
- **自动检测算法**：基于OpenCV的直线和圆形自动识别
- **参数化配置**：可调节的检测参数和相机设置
- **数据记录管理**：完整的操作日志和测量结果保存

### 1.3 系统要求
- **操作系统**：Windows 10/11 或 macOS 10.14+
- **Python环境**：Python 3.7+
- **硬件要求**：
  - 内存：8GB以上
  - 处理器：Intel i5或同等性能
  - 海康威视工业相机及驱动
  - USB 3.0接口

## 2. 软件安装与配置

### 2.1 环境准备
1. **安装Python环境**
   ```bash
   # 推荐使用Anaconda或Miniconda
   conda create -n multicam python=3.8
   conda activate multicam
   ```

2. **安装依赖库**
   ```bash
   pip install PyQt5
   pip install opencv-python
   pip install numpy
   pip install pillow
   ```

3. **安装海康威视SDK**
   - 下载并安装MVS（Machine Vision Software）
   - 确保相机驱动正确安装

### 2.2 软件启动
1. **命令行启动**
   ```bash
   cd /path/to/MutiCamApp
   python main.py
   ```

2. **首次运行配置**
   - 软件会自动创建Settings目录和配置文件
   - 检查相机连接状态
   - 加载默认参数设置

## 3. 界面介绍

### 3.1 主界面布局
软件采用标签页设计，包含以下主要界面：

#### 主界面标签页
- **三视图显示区域**：垂直视图、左侧视图、对向视图
- **测量功能区**：绘画工具、网格控制、自动测量、运行控制

#### 独立视图标签页
- **垂直视图**：单独的垂直相机视图和控制
- **左侧视图**：单独的左侧相机视图和控制
- **对向视图**：单独的对向相机视图和控制

#### 参数设置标签页
- **显示设置**：界面尺寸配置
- **相机参数**：相机SN码设置
- **测量参数**：直线和圆形检测参数

### 3.2 功能区域详解

#### 绘画功能区
- **绘制点**：在图像上标记关键点
- **绘制直线**：测量直线距离和角度
- **绘制简单圆**：快速圆形标记
- **绘制精细圆**：精确圆形测量
- **绘制平行线**：平行线距离测量
- **线与线**：两线交点和角度测量
- **撤销绘画**：撤销最后一次绘制操作
- **清空绘画**：清除所有绘制内容
- **像素距离标定**：设置像素与实际距离的比例

#### 网格功能区
- **网格密度**：设置网格间距（像素）
- **取消网格**：隐藏网格显示

#### 自动测量区
- **直线/圆查找**：自动检测图像中的直线或圆形
- **撤销上步**：撤销最后一次自动检测结果

#### 运行控制区
- **开始测量**：启动连续测量模式
- **停止测量**：停止测量模式
- **保存图像**：保存当前显示的图像

## 4. 基本操作流程

### 4.1 相机连接与设置

1. **检查相机连接**
   - 确保所有相机正确连接到计算机
   - 打开软件，系统会自动检测可用相机

2. **配置相机参数**
   - 切换到"参数设置"标签页
   - 在"相机采集参数"组中设置各相机的SN码
   - 根据实际硬件配置填写：
     - 垂直相机SN码
     - 左侧相机SN码
     - 对向相机SN码

3. **调整显示设置**
   - 设置"界面总宽"和"界面总高"
   - 根据显示器分辨率调整合适的尺寸

### 4.2 图像采集操作

1. **启动图像采集**
   - 返回"主界面"标签页
   - 点击"开始测量"按钮
   - 观察三个视图是否正常显示图像

2. **调整图像显示**
   - 使用鼠标滚轮可以缩放图像
   - 拖拽可以移动图像位置
   - 双击图像可以重置缩放

3. **网格辅助显示**
   - 在网格功能区输入网格密度值
   - 网格将叠加显示在图像上
   - 点击"取消网格"可隐藏网格

### 4.3 手动测量操作

1. **点测量**
   - 点击"绘制点"按钮
   - 在图像上点击要标记的位置
   - 系统会显示点的坐标信息

2. **直线测量**
   - 点击"绘制直线"按钮
   - 在图像上点击起点，再点击终点
   - 系统显示直线长度和角度信息

3. **圆形测量**
   - **简单圆**：点击"绘制简单圆"，在图像上拖拽绘制
   - **精细圆**：点击"绘制精细圆"，通过三点确定圆形
   - 系统显示圆心坐标、半径等信息

4. **高级测量**
   - **平行线测量**：测量两条平行线之间的距离
   - **线与线测量**：计算两条直线的交点和夹角

5. **测量管理**
   - 使用"撤销绘画"撤销最后一次操作
   - 使用"清空绘画"清除所有测量标记

### 4.4 自动检测操作

1. **参数配置**
   - 切换到"参数设置"标签页
   - 在"自动测量参数"组中调整检测参数

2. **直线检测参数**
   - **边缘低阈值**：Canny边缘检测的低阈值
   - **边缘高阈值**：Canny边缘检测的高阈值
   - **最小线长**：检测直线的最小长度
   - **最大间隙**：直线段之间的最大间隙

3. **圆形检测参数**
   - **边缘低阈值**：圆形检测的边缘低阈值
   - **边缘高阈值**：圆形检测的边缘高阈值
   - **圆查找阈值**：HoughCircles算法的累加器阈值

4. **执行自动检测**
   - 返回测量界面
   - 点击"直线/圆查找"按钮
   - 系统自动检测并标记找到的几何形状
   - 使用"撤销上步"可以撤销检测结果

### 4.5 数据保存与管理

1. **图像保存**
   - 点击"保存图像"按钮
   - 选择保存路径和文件名
   - 支持常见图像格式（PNG、JPG、BMP等）

2. **测量结果记录**
   - 所有测量操作都会自动记录到日志
   - 日志文件按日期分类保存
   - 包含操作时间、测量类型、结果数据等信息

3. **设置保存**
   - 软件会自动保存用户的参数设置
   - 下次启动时自动加载上次的配置

## 5. 高级功能

### 5.1 像素距离标定

1. **标定目的**
   - 将像素单位转换为实际物理单位（如毫米）
   - 提高测量精度和实用性

2. **标定步骤**
   - 在图像中放置已知尺寸的标准件
   - 使用"绘制直线"测量标准件的像素长度
   - 点击"像素距离标定"按钮
   - 输入标准件的实际长度
   - 系统自动计算像素比例系数

3. **标定后的测量**
   - 所有后续测量都会显示实际物理尺寸
   - 标定参数会保存到配置文件

### 5.2 多视图协同测量

1. **三视图联动**
   - 可以在不同视图中进行独立测量
   - 每个视图都有完整的测量功能
   - 测量结果可以相互参考和验证

2. **视图切换**
   - 使用标签页快速切换不同视图
   - 每个视图保持独立的测量状态
   - 支持同时显示多个视图的测量结果

### 5.3 批量处理

1. **连续测量模式**
   - 启动"开始测量"后进入连续模式
   - 可以连续进行多次测量操作
   - 所有结果都会记录到日志文件

2. **自动化流程**
   - 设置好检测参数后
   - 可以重复执行自动检测
   - 适用于批量产品检测

## 6. 故障排除

### 6.1 常见问题

#### 相机连接问题
**问题**：软件启动后看不到相机图像
**解决方案**：
1. 检查相机电源和USB连接
2. 确认海康威视驱动正确安装
3. 检查相机SN码设置是否正确
4. 重启软件或重新插拔相机

#### 图像显示异常
**问题**：图像显示模糊或颜色异常
**解决方案**：
1. 检查相机镜头是否清洁
2. 调整相机焦距和光圈设置
3. 检查光照条件是否充足
4. 重新启动相机采集

#### 测量精度问题
**问题**：测量结果不准确
**解决方案**：
1. 重新进行像素距离标定
2. 检查图像是否有畸变
3. 确保测量对象清晰可见
4. 调整检测参数阈值

#### 自动检测失效
**问题**：自动检测找不到目标
**解决方案**：
1. 调整边缘检测阈值参数
2. 改善图像对比度和光照
3. 检查目标是否在检测范围内
4. 尝试手动测量验证目标存在

### 6.2 性能优化

#### 提高响应速度
1. 关闭不必要的视图标签页
2. 降低图像分辨率（如果精度允许）
3. 减少同时运行的其他程序
4. 使用SSD硬盘提高文件读写速度

#### 内存管理
1. 定期清空绘画内容
2. 避免长时间连续运行
3. 监控系统内存使用情况
4. 必要时重启软件释放内存

## 7. 维护与更新

### 7.1 日常维护

1. **日志文件管理**
   - 定期清理过期的日志文件
   - 备份重要的测量记录
   - 监控日志文件大小

2. **配置文件备份**
   - 定期备份Settings/settings.json文件
   - 记录重要的参数配置
   - 建立配置版本管理

3. **硬件维护**
   - 定期清洁相机镜头
   - 检查USB连接线状态
   - 确保相机固定稳定

### 7.2 软件更新

1. **版本检查**
   - 关注软件更新通知
   - 查看更新日志了解新功能
   - 评估更新的必要性

2. **更新流程**
   - 备份当前配置和数据
   - 下载新版本软件
   - 测试新功能的兼容性
   - 逐步迁移到新版本

## 8. 技术支持

### 8.1 联系方式
- **技术支持邮箱**：<EMAIL>
- **用户手册更新**：定期查看软件目录下的文档
- **在线帮助**：软件内置帮助系统

### 8.2 反馈建议
- 使用过程中的问题和建议
- 功能改进需求
- 性能优化建议
- 新功能需求

---

**版本信息**：v1.0
**更新日期**：2024年12月
**适用软件版本**：MutiCamApp v1.0+

*本说明书将根据软件更新持续完善，请关注最新版本。*
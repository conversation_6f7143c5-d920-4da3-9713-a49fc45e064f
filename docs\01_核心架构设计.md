# MutiCamApp C++ 核心架构设计文档

## 1. 架构概述

### 1.1 设计原则
本项目采用现代C++和Qt6框架，遵循以下设计原则：

- **SOLID原则**：单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
- **模块化设计**：高内聚、低耦合的模块结构
- **接口导向**：基于抽象接口的编程模式
- **依赖注入**：通过服务定位器管理依赖关系
- **分层架构**：清晰的层次结构和职责分离

### 1.2 技术栈
- **核心框架**：Qt6 (GUI、信号槽、多线程)
- **图像处理**：OpenCV 4.x (计算机视觉算法)
- **相机SDK**：海康威视MVS SDK (工业相机控制)
- **构建系统**：CMake 3.20+
- **编程语言**：C++17标准

## 2. 模块架构

### 2.1 核心模块结构
```
src/core/
├── camera/                 # 相机控制模块
│   ├── camera_controller.h     # 相机控制器接口
│   └── hikvision_camera.h      # 海康相机实现
├── measurement/            # 测量管理模块
│   └── measurement_manager.h   # 测量管理器接口
├── drawing/               # 绘图管理模块
│   └── drawing_manager.h      # 绘图管理器接口
├── data/                  # 数据管理模块
│   └── data_manager.h         # 数据管理器接口
├── window_manager/                    # UI管理模块
│   └── window_manager.h           # UI管理器接口
└── core_interfaces.h      # 核心接口统一定义
```

### 2.2 模块职责划分

#### 2.2.1 相机控制模块 (Camera)
- **ICameraController**: 相机控制器抽象接口
- **HikvisionCamera**: 海康威视相机具体实现
- **CameraManager**: 多相机管理器
- **CameraThread**: 相机采集线程

**核心功能**：
- 相机设备枚举和连接
- 图像采集和参数控制
- 多相机同步管理
- 线程安全的图像获取

#### 2.2.2 测量管理模块 (Measurement)
- **IMeasurementManager**: 测量管理器接口
- **GeometryCalculator**: 几何计算工具类
- **DrawingType**: 绘制类型枚举
- **MeasurementResult**: 测量结果结构

**核心功能**：
- 手动测量工具（点、线、圆、角度等）
- 自动检测算法（直线检测、圆检测）
- 几何计算和距离测量
- 像素标定和单位转换

#### 2.2.3 绘图管理模块 (Drawing)
- **IDrawingManager**: 绘图管理器接口
- **IImageDisplayWidget**: 图像显示控件接口
- **LayerManager**: 图层管理器
- **DrawingStateManager**: 绘制状态管理器

**核心功能**：
- 图像渲染和显示
- 几何对象绘制
- 缩放和视图控制
- 多层级渲染管理

#### 2.2.4 数据管理模块 (Data)
- **ISettingsManager**: 设置管理器接口
- **IDataRecorder**: 数据记录器接口
- **IDataExporter**: 数据导出器接口
- **IProjectManager**: 项目管理器接口
- **ILogManager**: 日志管理器接口

**核心功能**：
- 配置参数管理
- 测量数据记录
- 数据导出和报告生成
- 项目文件管理
- 日志记录和管理

#### 2.2.5 UI管理模块 (UI)
- **IMainWindowManager**: 主窗口管理器接口
- **IDockManager**: 停靠窗口管理器接口
- **IToolBarManager**: 工具栏管理器接口
- **IStatusBarManager**: 状态栏管理器接口
- **IMenuManager**: 菜单管理器接口
- **IDialogManager**: 对话框管理器接口

**核心功能**：
- 主窗口布局管理
- 停靠窗口和工具栏控制
- 菜单和快捷键管理
- 对话框和消息提示
- 主题和样式管理

## 3. 核心设计模式

### 3.1 接口隔离模式
每个模块都定义了清晰的抽象接口，实现了接口与实现的分离：
```cpp
// 示例：相机控制器接口
class ICameraController : public QObject {
public:
    virtual bool connect(const std::string& serialNumber) = 0;
    virtual bool startStreaming() = 0;
    virtual cv::Mat getLatestFrame() = 0;
    // ...
};
```

### 3.2 工厂模式
通过模块工厂创建各种组件实例：
```cpp
class IModuleFactory {
public:
    virtual std::shared_ptr<Camera::CameraManager> createCameraManager() = 0;
    virtual std::shared_ptr<Measurement::IMeasurementManager> createMeasurementManager() = 0;
    // ...
};
```

### 3.3 服务定位器模式
提供全局访问各个核心服务的统一入口：
```cpp
class ServiceLocator {
public:
    static ServiceLocator& instance();
    std::shared_ptr<ICoreApplication> getCoreApplication();
    std::shared_ptr<Camera::CameraManager> getCameraManager();
    // ...
};
```

### 3.4 观察者模式
基于Qt信号槽机制实现模块间通信：
```cpp
signals:
    void imageReceived(const cv::Mat& image);
    void measurementCompleted(const MeasurementResult& result);
    void cameraStateChanged(CameraState state);
```

## 4. 数据流设计

### 4.1 图像数据流
```
相机硬件 → CameraController → CameraThread → ImageDisplayWidget
                                    ↓
                              MeasurementManager → DrawingManager
```

### 4.2 测量数据流
```
用户交互 → MeasurementManager → GeometryCalculator → MeasurementResult
                                                           ↓
                                                    DataRecorder
```

### 4.3 配置数据流
```
UI设置 → SettingsManager → 配置文件
                    ↓
              各模块参数更新
```

## 5. 线程模型

### 5.1 主线程
- UI事件处理
- 用户交互响应
- 模块协调控制

### 5.2 相机线程
- 图像采集循环
- 相机参数控制
- 图像数据传输

### 5.3 计算线程
- 图像处理算法
- 几何计算
- 自动检测算法

### 5.4 IO线程
- 文件读写操作
- 数据导出
- 日志记录

## 6. 内存管理

### 6.1 智能指针使用
- `std::shared_ptr`: 模块间共享的对象
- `std::unique_ptr`: 模块内部独占的对象
- `std::weak_ptr`: 避免循环引用

### 6.2 图像数据管理
- OpenCV Mat对象的引用计数
- 图像缓存池管理
- 内存使用监控

## 7. 错误处理策略

### 7.1 异常安全
- RAII资源管理
- 异常安全的接口设计
- 错误状态传播机制

### 7.2 错误报告
- 统一的错误码定义
- 详细的错误信息记录
- 用户友好的错误提示

## 8. 扩展性设计

### 8.1 插件架构预留
- 基于接口的插件系统
- 动态加载机制
- 插件生命周期管理

### 8.2 算法扩展
- 可插拔的检测算法
- 自定义测量工具
- 第三方库集成接口

## 9. 性能优化考虑

### 9.1 图像处理优化
- 多线程并行处理
- GPU加速预留接口
- 内存池和对象池

### 9.2 UI响应优化
- 异步操作设计
- 进度反馈机制
- 延迟加载策略

## 10. 下一步实施计划

### 10.1 第二阶段：基础实现
- 实现各模块的具体类
- 完成基础功能开发
- 模块间集成测试

### 10.2 第三阶段：高级功能
- 自动检测算法实现
- 数据导出功能
- UI美化和优化

### 10.3 第四阶段：优化完善
- 性能优化
- 错误处理完善
- 文档和测试补充

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**负责人**: MutiCamApp开发团队
<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1014</width>
    <height>734</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>512</width>
    <height>400</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>靶装配对接测量软件</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QTabWidget" name="tabWidget">
      <property name="font">
       <font>
        <pointsize>12</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabMian">
       <attribute name="title">
        <string>主界面</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_11" stretch="1,1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>垂直视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_3">
             <property name="sizeConstraint">
              <enum>QLayout::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbVerticalView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>左侧视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_11">
             <property name="sizeConstraint">
              <enum>QLayout::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbLeftView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_19" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBox_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>对向视图</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_4">
             <property name="sizeConstraint">
              <enum>QLayout::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <property name="spacing">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="lbFrontView">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_4">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Ignored" vsizetype="Ignored">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="acceptDrops">
             <bool>false</bool>
            </property>
            <property name="title">
             <string>测量</string>
            </property>
            <property name="flat">
             <bool>false</bool>
            </property>
            <layout class="QGridLayout" name="gridLayout_6">
             <item row="0" column="0">
              <widget class="QGroupBox" name="groupBox_6">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="layoutDirection">
                <enum>Qt::LeftToRight</enum>
               </property>
               <property name="title">
                <string>绘画</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="flat">
                <bool>true</bool>
               </property>
               <property name="checkable">
                <bool>false</bool>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_10" stretch="1,1,0,0,0">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_2">
                  <item>
                   <widget class="QPushButton" name="btnDrawPoint">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>点</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnDrawStraight">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>直线</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnDrawSimpleCircle">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <family>Agency FB</family>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>简单圆</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_3">
                  <item>
                   <widget class="QPushButton" name="btnDrawParallel">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>平行线</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnDraw2Line">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>线与线</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnDrawFineCircle">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <family>Agency FB</family>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>精细圆</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QPushButton" name="btnCan1StepDraw">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>12</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>撤销上步绘画</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btnClearDrawings">
                  <property name="font">
                   <font>
                    <pointsize>12</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>清空绘画</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btnSaveImage">
                  <property name="font">
                   <font>
                    <pointsize>12</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>保存图像（原始+可视化）</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="0" column="1">
              <layout class="QVBoxLayout" name="verticalLayout" stretch="2,2,3">
               <item>
                <widget class="QGroupBox" name="groupBox_5">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="layoutDirection">
                  <enum>Qt::LeftToRight</enum>
                 </property>
                 <property name="title">
                  <string>网格</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="flat">
                  <bool>true</bool>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_5">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0">
                    <item>
                     <widget class="QLabel" name="label">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="layoutDirection">
                       <enum>Qt::LeftToRight</enum>
                      </property>
                      <property name="text">
                       <string>网格密度:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
                     <widget class="QLineEdit" name="leGridDens">
                      <property name="maximumSize">
                       <size>
                        <width>90</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QLabel" name="label_23">
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <weight>75</weight>
                        <bold>true</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>像素</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnCancelGrids">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>取消网格</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_16">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="layoutDirection">
                  <enum>Qt::LeftToRight</enum>
                 </property>
                 <property name="title">
                  <string>自动测量</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="flat">
                  <bool>true</bool>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_6">
                  <item>
                   <widget class="QPushButton" name="btnLineDet">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>直线查找</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnCircleDet">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>圆查找</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnCan1StepDet">
                    <property name="font">
                     <font>
                      <pointsize>9</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="text">
                     <string>撤销上步</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_8">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="layoutDirection">
                  <enum>Qt::LeftToRight</enum>
                 </property>
                 <property name="title">
                  <string>运行</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="flat">
                  <bool>true</bool>
                 </property>
                 <property name="checkable">
                  <bool>false</bool>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_4">
                  <item>
                   <widget class="QPushButton" name="btnStartMeasure">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>12</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">background-color: rgb(0, 170, 0);</string>
                    </property>
                    <property name="text">
                     <string>开始测量</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="btnStopMeasure">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>12</pointsize>
                      <weight>75</weight>
                      <bold>true</bold>
                     </font>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">background-color: rgb(170, 0, 0);</string>
                    </property>
                    <property name="text">
                     <string>停止测量</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabVerticalView">
       <attribute name="title">
        <string>垂直视图</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_8">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_10">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="acceptDrops">
           <bool>false</bool>
          </property>
          <property name="title">
           <string/>
          </property>
          <property name="flat">
           <bool>false</bool>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_8" stretch="1,1,3">
           <item>
            <widget class="QGroupBox" name="groupBox_11">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_12">
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="label_2">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="layoutDirection">
                 <enum>Qt::LeftToRight</enum>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDens_Ver">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="label_24">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGrids_Ver">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_15">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <property name="topMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QPushButton" name="btnLineDet_Ver">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDet_Ver">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDet_Ver">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消上步结果</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_12">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="title">
              <string>绘画</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_19">
              <item row="4" column="0">
               <widget class="QPushButton" name="btnDrawParallel_Ver">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item row="8" column="0">
               <widget class="QPushButton" name="btnCalibration_Ver">
                <property name="text">
                 <string>像素距离标定</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QPushButton" name="btnDrawFineCircle_Ver">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精细圆</string>
                </property>
               </widget>
              </item>
              <item row="6" column="0">
               <widget class="QPushButton" name="btnCan1StepDraw_Ver">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销上步绘画</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QPushButton" name="btnDrawStraight_Ver">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item row="7" column="0">
               <widget class="QPushButton" name="btnClearDrawings_Ver">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QPushButton" name="btnDrawPoint_Ver">
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QPushButton" name="btnDraw2Line_Ver">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>线与线</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QPushButton" name="btnDrawSimpleCircle_Ver">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QGroupBox" name="groupBox_9">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_7">
           <item row="0" column="0">
            <widget class="QLabel" name="lbVerticalView_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="btnSaveImage_Ver">
             <property name="text">
              <string>保存图像（原始图像+可视化图像）</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabLeftView">
       <attribute name="title">
        <string>左侧视图</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_20">
        <item>
         <widget class="QGroupBox" name="groupBox_13">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="acceptDrops">
           <bool>false</bool>
          </property>
          <property name="title">
           <string/>
          </property>
          <property name="flat">
           <bool>false</bool>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_12" stretch="1,1,3">
           <item>
            <widget class="QGroupBox" name="groupBox_14">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_13">
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="label_5">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDens_Left">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="label_34">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGrids_Left">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_17">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_13">
              <property name="topMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QPushButton" name="btnLineDet_Left">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDet_Left">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDet_Left">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消上步结果</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_18">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="title">
              <string>绘画</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_20">
              <item row="7" column="0">
               <widget class="QPushButton" name="btnClearDrawings_Left">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QPushButton" name="btnDrawFineCircle_Left">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精细圆</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QPushButton" name="btnDrawParallel_Left">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item row="6" column="0">
               <widget class="QPushButton" name="btnCan1StepDraw_Left">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销上步绘画</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QPushButton" name="btnDrawStraight_Left">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QPushButton" name="btnDrawPoint_Left">
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QPushButton" name="btnDrawSimpleCircle_Left">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QPushButton" name="btnDraw2Line_Left">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>线与线</string>
                </property>
               </widget>
              </item>
              <item row="8" column="0">
               <widget class="QPushButton" name="btnCalibration_Left">
                <property name="text">
                 <string>像素距离标定</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_21">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_5">
           <item row="0" column="0">
            <widget class="QLabel" name="lbLeftView_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="btnSaveImage_Left">
             <property name="text">
              <string>保存图像（原始图像+可视化图像）</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabFrontView">
       <attribute name="title">
        <string>对向视图</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_21">
        <item>
         <widget class="QGroupBox" name="groupBox_19">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="acceptDrops">
           <bool>false</bool>
          </property>
          <property name="title">
           <string/>
          </property>
          <property name="flat">
           <bool>false</bool>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_14" stretch="1,1,3">
           <item>
            <widget class="QGroupBox" name="groupBox_20">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>网格</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_14">
              <item row="2" column="0" colspan="2">
               <widget class="QPushButton" name="btnCancelGrids_Front">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消网格</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLabel" name="label_35">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string>像素</string>
                </property>
               </widget>
              </item>
              <item row="0" column="0" colspan="2">
               <widget class="QLabel" name="label_6">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>输入网格密度</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLineEdit" name="leGridDens_Front">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>70</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_22">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::DefaultContextMenu</enum>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="title">
              <string>自动测量</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_17">
              <property name="topMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QPushButton" name="btnLineDet_Front">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCircleDet_Front">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>圆查找</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="btnCan1StepDet_Front">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>取消上步结果</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_26">
             <property name="font">
              <font>
               <pointsize>9</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="title">
              <string>绘画</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <layout class="QGridLayout" name="gridLayout_21">
              <item row="0" column="0">
               <widget class="QPushButton" name="btnDrawPoint_Front">
                <property name="text">
                 <string>点</string>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QPushButton" name="btnDraw2Line_Front">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>线与线</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QPushButton" name="btnDrawSimpleCircle_Front">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>简单圆</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QPushButton" name="btnDrawFineCircle_Front">
                <property name="font">
                 <font>
                  <family>Agency FB</family>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>精细圆</string>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QPushButton" name="btnDrawParallel_Front">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>平行线</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QPushButton" name="btnDrawStraight_Front">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>直线</string>
                </property>
               </widget>
              </item>
              <item row="7" column="0">
               <widget class="QPushButton" name="btnClearDrawings_Front">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>清空绘画</string>
                </property>
               </widget>
              </item>
              <item row="6" column="0">
               <widget class="QPushButton" name="btnCan1StepDraw_Front">
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="text">
                 <string>撤销上步绘画</string>
                </property>
               </widget>
              </item>
              <item row="8" column="0">
               <widget class="QPushButton" name="btnCalibration_Front">
                <property name="text">
                 <string>像素距离标定</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_23">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_9">
           <item row="0" column="0">
            <widget class="QLabel" name="lbFrontView_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="btnSaveImage_Front">
             <property name="text">
              <string>保存图像（原始图像+可视化图像）</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabSettings">
       <attribute name="title">
        <string>参数设置</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout_15">
          <item row="0" column="0">
           <widget class="QGroupBox" name="groupBox_36">
            <property name="title">
             <string>显示设置</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_16">
             <item row="2" column="0">
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="3" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_23" stretch="2,0,1,3">
               <item>
                <widget class="QLabel" name="label_31">
                 <property name="text">
                  <string>界面总长:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ledUIHeight"/>
               </item>
               <item>
                <widget class="QLabel" name="label_33">
                 <property name="text">
                  <string>像素</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_11">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="1" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_22" stretch="2,0,1,3">
               <item>
                <widget class="QLabel" name="label_30">
                 <property name="text">
                  <string>界面总宽:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ledUIWidth"/>
               </item>
               <item>
                <widget class="QLabel" name="label_32">
                 <property name="text">
                  <string>像素</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_10">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item row="4" column="0">
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
             <item row="0" column="0">
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <widget class="QGroupBox" name="groupBox_24">
          <property name="title">
           <string>相机采集参数</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_18">
           <item row="2" column="0">
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="3" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="label_10">
               <property name="font">
                <font>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>左侧相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledLeftCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_8">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_11">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="6" column="0">
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="0">
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="label_8">
               <property name="font">
                <font>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>垂直相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledVerCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_7">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_9">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="5" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="4,4,4,0">
             <item>
              <widget class="QLabel" name="label_12">
               <property name="font">
                <font>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>对向相机SN码：</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="ledFrontCamSN"/>
             </item>
             <item>
              <spacer name="horizontalSpacer_9">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_13">
               <property name="font">
                <font>
                 <family>Times New Roman</family>
                 <pointsize>9</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="4" column="0">
            <spacer name="horizontalSpacer_5">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="1" rowspan="2">
         <widget class="QGroupBox" name="groupBox_25">
          <property name="title">
           <string>自动测量参数</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_17">
           <item row="0" column="0">
            <widget class="QGroupBox" name="groupBox_34">
             <property name="font">
              <font>
               <pointsize>11</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>直线查找参数</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_9">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_18" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_28">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘低阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyLineLow">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_12">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_24" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_27">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘高阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyLineHigh">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_13">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_25" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_26">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>直线查找阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetThreshold">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_14">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_26" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_29">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>最小线长</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetMinLength">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_15">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_27" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_25">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>最大间隙</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledLineDetMaxGap">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_16">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QGroupBox" name="groupBox_35">
             <property name="font">
              <font>
               <pointsize>11</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="title">
              <string>圆查找参数</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_28" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_3">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘低阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyCircleLow">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_17">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_29" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_21">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>边缘高阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCannyCircleHigh">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_18">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_30" stretch="3,1,5">
                <item>
                 <widget class="QLabel" name="label_22">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>圆查找阈值</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="ledCircleDetParam2">
                  <property name="font">
                   <font>
                    <pointsize>11</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_19">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_31" stretch="5">
                <item>
                 <spacer name="horizontalSpacer_20">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_32" stretch="5">
                <item>
                 <spacer name="horizontalSpacer_21">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>

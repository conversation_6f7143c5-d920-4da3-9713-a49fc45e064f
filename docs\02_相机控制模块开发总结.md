# 第二阶段：相机控制模块开发总结

## 概述

本阶段完成了MutiCamApp项目的相机控制模块开发，实现了海康威视SDK的完整封装和多线程图像采集功能。该模块为整个应用程序提供了稳定、高效的相机控制基础。

## 主要成果

### 1. 海康威视SDK封装

#### 1.1 HikvisionCamera类实现
- **文件位置**: `src/core/camera/hikvision_camera.h/cpp`
- **核心功能**:
  - 设备枚举和连接管理
  - 图像采集和流式传输
  - 相机参数设置（曝光、增益、帧率、像素格式）
  - 触发模式和软件触发支持
  - 性能统计和错误处理

#### 1.2 主要接口实现
```cpp
// 设备管理
static std::vector<std::string> enumerateDevices();
bool connect(const std::string& serialNumber);
void disconnect();

// 流式传输控制
bool startStreaming();
bool stopStreaming();

// 参数设置
bool setExposureTime(double exposureTime);
bool setGain(double gain);
bool setFrameRate(double frameRate);
bool setPixelFormat(const std::string& format);

// 触发功能
bool setTriggerMode(bool enabled);
bool softwareTrigger();
```

#### 1.3 技术特性
- **线程安全**: 使用QMutex确保多线程环境下的安全访问
- **错误处理**: 完善的错误检测和报告机制
- **性能监控**: 实时帧率统计和性能分析
- **像素格式支持**: Mono8、RGB8_Packed等多种格式
- **自动转换**: 不支持格式自动转换为Mono8

### 2. 多线程图像采集

#### 2.1 CameraThread类实现
- **文件位置**: `src/core/camera/camera_thread.h/cpp`
- **设计理念**: 为每个相机提供独立的采集线程，避免多相机采集时的阻塞问题

#### 2.2 核心功能
- **独立线程采集**: 每个相机运行在独立线程中
- **帧率控制**: 支持最大帧率限制，避免系统过载
- **暂停/恢复**: 支持采集过程的暂停和恢复
- **性能统计**: 实时FPS计算和总帧数统计
- **状态管理**: 完整的采集状态跟踪

#### 2.3 关键特性
```cpp
// 采集控制
void startCapture();
void stopCapture();
void pauseCapture();
void resumeCapture();

// 性能管理
void setMaxFPS(double maxFPS);
double getCurrentFPS() const;
uint64_t getTotalFrameCount() const;

// 状态查询
bool isCapturing() const;
```

### 3. 相机管理器

#### 3.1 CameraManager类实现
- **文件位置**: `src/core/camera/camera_manager.h/cpp`
- **职责**: 统一管理多个相机实例和线程

#### 3.2 管理功能
- **相机生命周期**: 添加、移除、连接、断开
- **批量操作**: 同时控制多个相机的启动和停止
- **状态监控**: 实时跟踪所有相机的状态
- **设备发现**: 自动枚举可用的相机设备
- **统计信息**: 提供详细的相机运行统计

#### 3.3 核心接口
```cpp
// 相机管理
bool addCamera(const std::string& cameraId, const std::string& serialNumber);
bool removeCamera(const std::string& cameraId);
std::shared_ptr<ICameraController> getCamera(const std::string& cameraId);

// 批量操作
bool startAllCameras();
bool stopAllCameras();
void disconnectAllCameras();

// 状态查询
std::map<std::string, CameraState> getAllCameraStates() const;
static std::vector<std::string> enumerateDevices();
```

## 技术架构

### 1. 模块层次结构
```
CameraManager (管理层)
    ├── HikvisionCamera (SDK封装层)
    └── CameraThread (线程管理层)
```

### 2. 线程模型
- **主线程**: UI交互和相机管理
- **相机线程**: 每个相机独立的采集线程
- **回调线程**: 海康SDK的图像回调处理

### 3. 数据流设计
```
海康SDK → HikvisionCamera → CameraThread → CameraManager → 应用层
```

### 4. 信号槽机制
- **frameReady**: 新帧就绪通知
- **stateChanged**: 相机状态变化
- **errorOccurred**: 错误事件通知
- **fpsUpdated**: 帧率更新通知

## 性能特性

### 1. 多相机并发
- 支持同时管理多个相机
- 每个相机独立线程，避免相互影响
- 线程安全的资源管理

### 2. 性能优化
- 帧率限制机制，防止系统过载
- 高效的图像数据转换
- 最小化内存拷贝

### 3. 错误恢复
- 完善的错误检测和报告
- 自动重连机制（预留接口）
- 优雅的资源清理

## 接口兼容性

### 1. 标准接口实现
- 完全实现了`ICameraController`接口
- 遵循第一阶段定义的接口规范
- 保持与其他模块的兼容性

### 2. 扩展性设计
- 支持添加其他品牌相机的SDK封装
- 模块化设计，便于功能扩展
- 标准化的错误处理和状态管理

## 测试验证

### 1. 功能测试覆盖
- [x] 设备枚举和连接
- [x] 图像采集和流式传输
- [x] 参数设置和获取
- [x] 触发模式和软件触发
- [x] 多线程并发采集
- [x] 错误处理和恢复

### 2. 性能测试
- [x] 单相机最大帧率测试
- [x] 多相机并发性能测试
- [x] 长时间稳定性测试
- [x] 内存泄漏检测

## 已知限制

### 1. 硬件依赖
- 需要安装海康威视MVS SDK
- 仅支持海康威视相机设备
- 依赖相机硬件的具体性能

### 2. 功能限制
- 当前仅实现基础参数设置
- 高级功能（如ROI、LUT等）待后续扩展
- 网络相机的高级配置功能有限

## 下一阶段准备

### 1. 接口就绪
相机控制模块已为第三阶段（测量管理模块）提供了完整的图像数据接口：
- `frameReady`信号提供实时图像数据
- 支持多种像素格式的图像输出
- 稳定的多线程图像采集能力

### 2. 扩展预留
- 预留了相机标定接口
- 支持添加图像预处理功能
- 为测量算法提供了稳定的数据源

## 文件清单

### 新增文件
1. `src/core/camera/camera_thread.h` - 相机线程类声明
2. `src/core/camera/camera_thread.cpp` - 相机线程类实现
3. `src/core/camera/camera_manager.h` - 相机管理器类声明
4. `docs/02_相机控制模块开发总结.md` - 本文档

### 完善文件
1. `src/core/camera/hikvision_camera.cpp` - 完善海康SDK封装实现
2. `src/core/camera/camera_manager.cpp` - 完善相机管理器实现

## 总结

第二阶段的相机控制模块开发已圆满完成，实现了以下核心目标：

✅ **海康SDK完整封装** - 提供了稳定可靠的相机控制接口  
✅ **多线程图像采集** - 实现了高效的并发图像采集能力  
✅ **统一管理接口** - 提供了便于使用的相机管理功能  
✅ **性能优化设计** - 确保了系统的稳定性和高性能  
✅ **完善错误处理** - 提供了健壮的错误检测和恢复机制  

该模块为后续的测量管理模块提供了坚实的基础，确保了图像数据的稳定供应和高质量处理能力。模块设计遵循了SOLID原则，具有良好的可扩展性和可维护性，为整个项目的成功奠定了重要基础。